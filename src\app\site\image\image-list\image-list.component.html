<main class="main-layout">
    <div class="pages-layout">
        <div class="pages-title h2">{{title}}</div>
        <!--內容放置區-->
        <div class="pages-content">
            <div class="pages-content-lsit">
                <!--列表-->
                <div class="news-pic-layout">
                    <div class="news-pic-cont">
                        @for (item of imageList; track item) {
                        <article class="news-pic-cont-item" (click)="goImage(item.typeGroupId)">
                            <div class="news-pic-cont-item-img">
                                <img [src]="item.coverUrl" [alt]="item.coverDescription">
                            </div>
                            <div class="news-pic-cont-item-list">
                                <span className="news-pic-cont-item-date">{{item.title}}</span>
                                <div class="news-pic-cont-item-title h3">{{item.startTime}}</div>
                                <p class="news-pic-cont-item-text">
                                    {{item.description}}
                                </p>
                            </div>
                        </article>
                        }
                    </div>
                </div>
                @if(imageList.length < 1){ <div class="not-found">
                    <span>查無資料</span>
            </div>
            }
            @if(imageList.length > 0){
            <app-paginator [pageSize]="pageSize" [nowPage]="nowPage" [totalRecords]="totalCount"
                [pageShowCount]="pageShowCount" currentPageReportTemplate="第 {first} 到 {last} 筆，共 {totalRecords} 筆"
                (clickPageEvent)="getPageFromPaginator($event)"
                (pageSizeChangeEvent)="getPageSizeFromPaginator($event)"></app-paginator>
            }
        </div>
    </div>
    </div>
</main>