import { defaultItem } from './share.interface';

export interface getENewsLetterListResp extends defaultItem {
  data: {
    currentPage: number;
    title: string;
    totalCount: number;
    totalPage: number;
    data: eNewsLetterItem[];
  };
}

export interface eNewsLetterItem {
  typeGroupId: string;
  e_NewsletterPublishDateTime: string;
  name: string;
}

export interface getENewsLetterDetailResp extends defaultItem {
  data: {
    title: string;
    content: string;
  };
}

export interface subscribeReq {
  email: string;
  reCaptcha: string;
}

export interface unsubscribeReq extends subscribeReq {}

export interface checkSubscribeResp extends defaultItem {
  data: {
    email: string;
  };
}

export interface checkUnsubscribeResp extends checkSubscribeResp {}
