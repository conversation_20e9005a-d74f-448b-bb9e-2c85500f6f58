// Scss Document
.quick-links {
	&-layout {
		padding: 30px 5px;
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 1.5rem;
		position: fixed;
		z-index: 999;
		right: 0;
		top: 8em;
	}

	&-content {
		// 預設狀態 (隱藏)
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 1.5rem;

		// 隱藏選單
		opacity: 0;
		transform: translateX(100%);
		pointer-events: none; // 避免隱藏時可以點擊

		// 過渡動畫效果，讓所有改變在 0.3 秒內平滑發生
		transition:
			opacity 0.3s ease-in-out,
			transform 0.3s ease-in-out;

		// 開啟狀態
		&.quick-links-open {
			opacity: 1;
			transform: translateX(0);
			pointer-events: auto; // 顯示時可以點擊
		}
	}

	&-item {
		cursor: pointer;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 5px;
		border-radius: 60px;
		width: 60px;
		height: 60px;
		background-color: #177691;
		color: #fff;
		box-shadow: 2px 2px 15px rgba(0, 0, 0, 0.1);
		&-icon {
			span {
				font-size: 2.1em;
			}
		}
		&-title {
			font-size: 0.875em;
			font-weight: bold;
		}
	}
	&-btn {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 5px;
		border-radius: 60px;
		width: 40px;
		height: 40px;
		border: 0;
		background-color: #177691;
		color: #fff;
		box-shadow: 2px 2px 15px rgba(0, 0, 0, 0.1);
		justify-content: center;
		&-title {
			display: none;
		}
	}
}
