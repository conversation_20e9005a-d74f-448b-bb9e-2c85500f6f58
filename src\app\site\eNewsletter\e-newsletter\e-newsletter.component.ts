import { Component } from '@angular/core';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { ENewsletterService } from '../../../core/services/enewsletter.service';
import { ActivatedRoute } from '@angular/router';
import { getENewsLetterDetailResp } from '../../../core/interface/enewsletter.interface';

@Component({
  selector: 'app-e-newsletter',
  standalone: false,

  templateUrl: './e-newsletter.component.html',
  styleUrl: './e-newsletter.component.scss',
})
export class ENewsletterComponent {
  title: string = '';
  data: SafeHtml = '';
  constructor(
    private sanitizer: DomSanitizer,
    private eNewsletterService: ENewsletterService,
    private activatedRoute: ActivatedRoute
  ) {
    this.activatedRoute.queryParamMap.subscribe((params) => {
      this.getENewsLetterDetail(params.get('typeGroupId')!);
    });
  }

  getENewsLetterDetail(typeGroupId: string) {
    this.eNewsletterService.getENewsLetterDetail(typeGroupId).subscribe({
      next: (resp: getENewsLetterDetailResp) => {
        this.data = this.sanitizer.bypassSecurityTrustHtml(resp.data.content);
        this.title = resp.data.title;
      },
      error: () => {},
    });
  }
}
