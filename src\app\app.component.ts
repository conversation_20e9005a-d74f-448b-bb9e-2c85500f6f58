import { Component } from '@angular/core';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  standalone: false,
  styleUrl: './app.component.scss',
})
export class AppComponent {
  constructor() {
    localStorage.setItem('webSiteId', 'C52CD7E3-2EFB-478E-BEAB-CBF98DB8309D');
    sessionStorage.setItem('webSiteId', 'C52CD7E3-2EFB-478E-BEAB-CBF98DB8309D');
  }

  fontSize: number = sessionStorage.getItem('fontSize')
    ? parseFloat(sessionStorage.getItem('fontSize') as string)
    : 1;

  changeFontSize(fontSize: number) {
    this.fontSize = fontSize;
  }
}
