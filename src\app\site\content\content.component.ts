import { Component, OnInit } from '@angular/core';
import { ContentService } from '../../core/services/content.service';
import { ActivatedRoute } from '@angular/router';
import { getContentDataResp } from '../../core/interface/content.interface';
import { dataItem } from '../../core/interface/type.interface';
import { SpinnerService } from '../../core/utils/spinner.service';

@Component({
  selector: 'app-content',
  standalone: false,

  templateUrl: './content.component.html',
  styleUrl: './content.component.scss',
})
export class ContentComponent implements OnInit {
  menuItemId: string = '';
  isPreview: boolean = false;
  title: string = '';
  contentData: dataItem[] = [];
  constructor(
    private activatedRoute: ActivatedRoute,
    private contentService: ContentService,
    private spinnerService: SpinnerService
  ) {
    this.activatedRoute.queryParamMap.subscribe((params) => {
      this.menuItemId = params.get('menuItemId')!;
      this.isPreview = (params.get('isPreview') as string) == 'true';
    });
  }

  ngOnInit(): void {
    if (this.isPreview) {
      this.getContentForView();
    } else {
      this.getContent();
    }
  }

  getContentForView() {
    this.spinnerService.show();
    this.contentService.getContentDataForView(this.menuItemId).subscribe({
      next: (resp: getContentDataResp) => {
        this.spinnerService.hide();
        this.title = resp.data.titleName;
        this.contentData = resp.data.new_NewsTagDatas;
      },
      error: () => {
        this.spinnerService.hide();
      },
    });
  }

  getContent() {
    this.spinnerService.show();
    this.contentService.getContentData(this.menuItemId).subscribe({
      next: (resp: getContentDataResp) => {
        this.spinnerService.hide();
        this.title = resp.data.titleName;
        this.contentData = resp.data.new_NewsTagDatas;
      },
      error: () => {
        this.spinnerService.hide();
      },
    });
  }
}
