import { defaultItem } from './share.interface';

export interface getImageListReq {
  menuitemId: string;
  currentPage: number;
  pageSize: number;
  lang: string;
}

export interface getImageListResp extends defaultItem {
  data: {
    menuitemId: string;
    title: string;
    totalCount: number;
    totalPage: number;
    currentPage: number;
    data: imageItem[];
  };
}
export interface imageItem {
  new_NewsId: string;
  title: string;
  typeGroupId: string;
  startTime: string;
  description: string;
  coverUrl: string;
  coverDescription: string;
  lang: string;
}
