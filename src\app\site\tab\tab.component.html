<main class="main-layout">
    <div class="pages-layout">
        <div class="pages-title h2">{{title}}</div>
        <!--頁籤：有就出現，沒有就隱藏-->
        <div class="tab-layout">
            <ul class="tab-content">
                @for (item of tabArr; let idx = $index; track item) {
                <li class="tab-content-item " [ngClass]="{'active': selectedTabIndex === $index}">
                    <a href="" (click)="selectTab(idx, item.id!, item.type!,$event)" class="tab-content-item-link">
                        {{item.name}}
                    </a>
                </li>
                }
            </ul>
        </div>
        <!--內容放置區-->
        @switch (selectedTabType) {
        @case (menuType.Content) {
        <app-tab-content [menuItemId]="selectedTabId"></app-tab-content>
        }
        @case (menuType.Html) {
        <app-tab-html [menuItemId]="selectedTabId"></app-tab-html>
        }
        @case (menuType.HtmlZip) {
        <app-tab-html-zip [menuItemId]="selectedTabId"></app-tab-html-zip>
        }
        @case (menuType.FAQ) {
        <app-tab-faq [menuItemId]="selectedTabId"></app-tab-faq>
        }
        @case (menuType.ShareResource) {
        <app-tab-share-resource></app-tab-share-resource>
        }
        @case (menuType.Video) {
        <app-tab-video></app-tab-video>
        }
        @case (menuType.Survey) {
        <app-tab-survey [menuItemId]="selectedTabId"></app-tab-survey>
        }
        }
    </div>
</main>