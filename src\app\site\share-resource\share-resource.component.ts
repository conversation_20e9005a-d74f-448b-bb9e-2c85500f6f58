import { Component } from '@angular/core';
import { ShareService } from '../../core/services/share.service';
import { ShareResourceService } from '../../core/services/share-resource.service';
import { ActivatedRoute } from '@angular/router';
import {
  getShareResourceListReq,
  getShareResourceListResp,
  shareResourceItem,
} from '../../core/interface/shareResource.interface';
import { SpinnerService } from '../../core/utils/spinner.service';

@Component({
  selector: 'app-share-resource',
  standalone: false,

  templateUrl: './share-resource.component.html',
  styleUrl: './share-resource.component.scss',
})
export class ShareResourceComponent {
  menuItemId: string = '';
  title: string = '';
  shareResourceList: shareResourceItem[] = [];
  totalCount: number = 0;
  nowPage: number = 1;
  pageSize: number = 10;
  pageShowCount: number = 10; //分頁器秀幾個

  constructor(
    private shareService: ShareService,
    private shareResourceService: ShareResourceService,
    private activatedRoute: ActivatedRoute,
    private spinnerService: SpinnerService
  ) {
    this.activatedRoute.queryParamMap.subscribe((params) => {
      this.menuItemId = params.get('menuItemId')!;
      this.getShareResource();
    });
  }

  getShareResource() {
    let req: getShareResourceListReq = {
      menuitemId: this.menuItemId,
      lang: this.shareService.getLang(),
      currentPage: this.nowPage,
      pageSize: this.pageSize,
    };
    this.spinnerService.show();
    this.shareResourceService.getShareResourceList(req).subscribe({
      next: (resp: getShareResourceListResp) => {
        this.spinnerService.hide();
        this.title = resp.data.title;
        this.totalCount = resp.data.totalCount;
        this.shareResourceList = resp.data.data;
      },
      error: () => {
        this.spinnerService.hide();
      },
    });
  }

  openFile(fileUrl: string) {
    window.open(fileUrl, '_blank');
  }

  /**
   * 第幾頁
   * @param item number
   */
  getPageFromPaginator(nowPage: number) {
    this.nowPage = nowPage;
    this.getShareResource();
  }
  /**
   * 每頁資料數量
   * @param item number
   */
  getPageSizeFromPaginator(pageSize: number) {
    this.pageSize = pageSize;
    this.nowPage = 1;
    this.getShareResource();
  }
}
