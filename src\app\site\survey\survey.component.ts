import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { SurveyService } from '../../core/services/survey.service';
import {
  getSurveyResp,
  sendSurveyReq,
  surveyField,
  surveyResultData,
} from '../../core/interface/survey.interface';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { defaultItem } from '../../core/interface/share.interface';
import Swal from 'sweetalert2';
import { HttpErrorResponse } from '@angular/common/http';
import { SpinnerService } from '../../core/utils/spinner.service';

export enum FieldType {
  Input = 'Input',
  Select = 'Select',
  TextArea = 'TextArea',
  Date = 'Date',
  Radio = 'Radio',
  CheckBox = 'CheckBox',
  Address = 'Address',
  Mail = 'Mail',
  Phone = 'Phone',
  IDnumber = 'IDnumber',
  ImageCheckBox = 'ImageCheckBox',
}

@Component({
  selector: 'app-survey',
  standalone: false,
  templateUrl: './survey.component.html',
  styleUrl: './survey.component.scss',
})
export class SurveyComponent implements OnInit {
  form: FormGroup;
  isPreview: boolean = false;
  menuItemId: string = '';
  typeGroupId: string = '';
  FieldType = FieldType;

  title: string = '';
  surveyId: string = '';
  surveyField: surveyField[] = [];

  constructor(
    private formBuilder: FormBuilder,
    private activatedRoute: ActivatedRoute,
    private surveyService: SurveyService,
    private spinnerService: SpinnerService
  ) {
    this.activatedRoute.queryParamMap.subscribe((params) => {
      this.menuItemId = params.get('menuItemId')!;
      this.isPreview = (params.get('isPreview') as string) == 'true';
    });
    this.form = this.formBuilder.group({});
  }

  ngOnInit() {
    this.getSurvey();
  }

  getSurvey() {
    this.spinnerService.show();
    this.surveyService.getSurvey(this.menuItemId).subscribe({
      next: (resp: getSurveyResp) => {
        this.spinnerService.hide();
        this.surveyField = resp.data.surveyField;
        this.surveyId = resp.data.surveyId;
        this.title = resp.data.title;
        this.surveyField.forEach((item) => {
          // 增加保護，防止 fieldMeta 為空或非字串時出錯
          if (typeof item.fieldMeta === 'string' && item.fieldMeta) {
            try {
              if (item.fieldType === FieldType.ImageCheckBox) {
                item.metaItems = JSON.parse(item.fieldMeta);
              } else if (
                item.fieldType === FieldType.Radio ||
                item.fieldType === FieldType.CheckBox ||
                item.fieldType === FieldType.Select
              ) {
                item.optionItems = JSON.parse(item.fieldMeta);
              }
            } catch (e) {
              console.error('Failed to parse fieldMeta:', item.fieldMeta, e);
            }
          }
        });

        this.buildForm();
      },
      error: (err) => {
        this.spinnerService.hide();
        console.error('Failed to get survey data:', err);
      },
    });
  }

  private buildForm() {
    const controls: { [key: string]: any } = {};

    this.surveyField.forEach((field) => {
      // 修正 #5: 屬性名稱要與你的 interface/API 一致，這裡假設是 isRequired
      const validators = field.required ? [Validators.required] : [];

      if (
        field.fieldType === FieldType.CheckBox ||
        field.fieldType === FieldType.ImageCheckBox
      ) {
        const choiceGroup = new FormGroup({});
        const options = field.optionItems || field.metaItems || [];
        options.forEach((option) => {
          choiceGroup.addControl(option.value, new FormControl(false));
        });
        controls[field.fieldId] = choiceGroup;
      } else {
        // 修正 #6: 預設值用 null，更適合 select 的 placeholder
        controls[field.fieldId] = new FormControl(null, validators);
      }
    });

    this.form = this.formBuilder.group(controls);
  }

  submit() {
    if (this.form.invalid) {
      this.form.markAllAsTouched();
      return;
    }

    const surveyResultData: surveyResultData[] = [];
    const formValue = this.form.value;

    for (const fieldId in formValue) {
      if (formValue.hasOwnProperty(fieldId)) {
        let value = formValue[fieldId];
        if (typeof value === 'object' && value !== null) {
          const selectedOptions = Object.keys(value).filter(
            (key) => value[key] === true
          );
          value = selectedOptions.join(',');
        } else {
          value = value !== null ? String(value) : '';
        }

        surveyResultData.push({
          surveyFieldId: fieldId,
          value: value,
        });
      }
    }

    let req: sendSurveyReq = {
      surveyId: this.menuItemId,
      surveyResultData: surveyResultData,
    };
    this.spinnerService.show();
    this.surveyService.sendSurvey(req).subscribe({
      next: (resp: defaultItem) => {
        this.spinnerService.hide();
        if (resp.code === 200) {
          Swal.fire({
            title: '提交成功',
            text: '感謝您的填寫',
            icon: 'success',
            // showCancelButton: true,
            confirmButtonText: '確認',
            // cancelButtonText: '取消',
            reverseButtons: true, // 讓取消在左邊、確認在右邊（符合台灣習慣）
          });
        } else {
          Swal.fire('錯誤', resp.message, 'error');
        }
      },
      error: (err: HttpErrorResponse) => {
        this.spinnerService.hide();
        Swal.fire('錯誤', err.error.message, 'error');
      },
    });
  }
}
