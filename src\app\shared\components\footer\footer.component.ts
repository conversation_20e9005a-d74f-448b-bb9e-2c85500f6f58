import { Component, OnInit } from '@angular/core';
import { LANGUAGE } from '../../../enum/language.enum';
import { NavigationEnd, Router } from '@angular/router';
import { CookieService } from '../../../core/services/cookie.service';
import { environment } from '../../../../environments/environment';
import { LangList } from '../../data/lang';
import { UtilService } from '../../../core/utils/util.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-footer',
  standalone: false,

  templateUrl: './footer.component.html',
  styleUrl: './footer.component.scss',
})
export class FooterComponent implements OnInit {
  language: string = '';
  isCookie: boolean = false;
  deviceStatus: boolean = true;
  constructor(
    private router: Router,
    private cookieService: CookieService,
    private utilService: UtilService,
    private translateService: TranslateService
  ) {}
  ngOnInit(): void {
    this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        this.getLanguage();
        this.checkCookie();
        this.deviceStatus = this.utilService.checkDevice();
      }
    });
  }
  checkCookie() {
    this.cookieService.getCookie().subscribe((resp: string) => {
      this.isCookie = resp === 'True' ? true : false;
    });
  }

  getLanguage() {
    const lang = window.location.pathname.split('/')[1]; // 取得語言代碼
    const found = LangList.some((item) => item.value === lang); // 檢查是否在語言列表中
    this.language = found ? lang : 'zh-tw'; // 預設 'zh-tw'，避免無效語言
  }

  iKnow() {
    this.cookieService.setCookie().subscribe(() => {});
    this.isCookie = true;
  }

  siteMap(event: Event) {
    event.preventDefault();
    this.router.navigate(['siteMap']);
  }

  link(event: Event) {
    event.preventDefault();
    let link: string = '';
    switch (this.language) {
      case LANGUAGE.TW:
        link = `${environment.apiUrl}/zh-tw/33760ca3-6f81-456a-98c0-39f2c8ebcf9c/隱私權暨資訊安全保護政策/隱私權暨資訊安全保護政策/content`;
        break;
      case LANGUAGE.JP:
        link = `${environment.apiUrl}/jp/92c68ce7-09a4-4de7-be74-f658c8e6f417/%E3%83%97%E3%83%A9%E3%82%A4%E3%83%90%E3%82%B7%E3%83%BC%E3%81%8A%E3%82%88%E3%81%B3%E6%83%85%E5%A0%B1%E3%82%BB%E3%82%AD%E3%83%A5%E3%83%AA%E3%83%86%E3%82%A3%E3%83%9D%E3%83%AA%E3%82%B7%E3%83%BC/%E3%83%97%E3%83%A9%E3%82%A4%E3%83%90%E3%82%B7%E3%83%BC%E3%81%8A%E3%82%88%E3%81%B3%E6%83%85%E5%A0%B1%E3%82%BB%E3%82%AD%E3%83%A5%E3%83%AA%E3%83%86%E3%82%A3%E3%83%9D%E3%83%AA%E3%82%B7%E3%83%BC/content`;
        break;
      case LANGUAGE.EN:
        link = `${environment.apiUrl}/en/32e96744-ef40-4a75-901d-e785989c3000/Privacy%20and%20Information%20Security%20Policy/Privacy%20and%20Information%20Security%20Policy/content`;
        break;
      case LANGUAGE.KR:
        link = `${environment.apiUrl}/kr/40ed5fec-258d-4fb9-822b-47bd6ae1aa89/%EA%B0%9C%EC%9D%B8%EC%A0%95%EB%B3%B4%20%EB%B3%B4%ED%98%B8%20%EB%B0%8F%20%EC%A0%95%EB%B3%B4%EB%B3%B4%EC%95%88%20%EC%A0%95%EC%B1%85/%EA%B0%9C%EC%9D%B8%EC%A0%95%EB%B3%B4%20%EB%B3%B4%ED%98%B8%20%EB%B0%8F%20%EC%A0%95%EB%B3%B4%EB%B3%B4%EC%95%88%20%EC%A0%95%EC%B1%85/content`;
        break;
      default:
        link = `${environment.apiUrl}/zh-tw/33760ca3-6f81-456a-98c0-39f2c8ebcf9c/%E9%9A%B1%E7%A7%81%E6%AC%8A%E6%9A%A8%E8%B3%87%E8%A8%8A%E5%AE%89%E5%85%A8%E4%BF%9D%E8%AD%B7%E6%94%BF%E7%AD%96/%E9%9A%B1%E7%A7%81%E6%AC%8A%E6%9A%A8%E8%B3%87%E8%A8%8A%E5%AE%89%E5%85%A8%E4%BF%9D%E8%AD%B7%E6%94%BF%E7%AD%96/content`;
        break;
    }
    window.location.href = link;
  }
  emailStatus: boolean = true;
  validateEmail(email: string) {
    const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    this.emailStatus = emailPattern.test(email);
  }

  sendMewsLetter(email: string) {
    if (!this.emailStatus || email.length < 1) {
      this.translateService
        .get('footer')
        .subscribe((translatedMessage: { correctEmailFormat: string }) => {
          alert(translatedMessage.correctEmailFormat);
        });

      return;
    }
    this.utilService.sendMewsLetter(email).subscribe({
      next: (resp: { success: boolean; message: string }) => {
        if (resp.success) {
          this.translateService
            .get('footer')
            .subscribe((translatedMessage: { subscribeSuccess: string }) => {
              alert(translatedMessage.subscribeSuccess);
            });
        } else {
          alert(resp.message);
        }
      },
      error: () => {
        this.translateService
          .get('footer')
          .subscribe((translatedMessage: { subscribeFailed: string }) => {
            alert(translatedMessage.subscribeFailed);
          });
      },
    });
  }
}
