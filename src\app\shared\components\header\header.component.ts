import { Component, OnInit } from '@angular/core';
import { MenuItemService } from '../../../core/services/menu-item.service';
import {
  getMenuListResp,
  menuItem,
} from '../../../core/interface/menu.interface';
import { Router } from '@angular/router';
import { MenuType } from '../../../enum/menyType.enum';
import { AppComponent } from '../../../app.component';
import { UtilService } from '../../../core/utils/util.service';
import { TranslateService } from '@ngx-translate/core';

export enum FontSizeEunm {
  big = 1.2,
  middle = 1,
  small = 0.7,
}

@Component({
  selector: 'app-header',
  standalone: false,
  templateUrl: './header.component.html',
  styleUrl: './header.component.scss',
})
export class HeaderComponent implements OnInit {
  fontSize: FontSizeEunm = sessionStorage.getItem('fontSize')
    ? parseFloat(sessionStorage.getItem('fontSize') as string)
    : FontSizeEunm.middle;
  FontSize = FontSizeEunm;
  menuType = MenuType;
  menuList: menuItem[] = [];
  menuSubList: any = [];
  isPhoneMenuOpenStatus: boolean = false;
  menuId: string = '';
  lang: string = sessionStorage.getItem('language') || 'zh';
  constructor(
    private appComponent: AppComponent,
    private menuItemService: MenuItemService,
    private router: Router,
    private utilsService: UtilService,
    private translate: TranslateService
  ) {}
  ngOnInit(): void {
    window.addEventListener('resize', () => this.checkDevice());
    this.getMenu();
  }

  changeFontsize(event: Event, fontSize: FontSizeEunm) {
    event.preventDefault();
    this.fontSize = fontSize;
    this.appComponent.changeFontSize(fontSize);
    this.utilsService.setFontSize(fontSize);
  }

  getMenu() {
    this.menuItemService.getMenuList().subscribe({
      next: (resp: menuItem[]) => {
        this.menuList = resp;
      },
      error: () => {},
    });
  }

  clickMenu(item: menuItem) {
    if (item.type === this.menuType.Folder) {
      if (this.menuId === item.id) {
        this.menuId = '';
      } else {
        this.menuId = item.id;
        this.getSubMenu(item);
      }
      return;
    }

    this.menuId = '';

    if (item.type === this.menuType.HyperLink) {
      if (item.meta) {
        location.href = item.meta as string;
      }
      return;
    }
    console.log(item.id);
    this.router.navigate([`/${item.type}/`], {
      queryParams: {
        menuItemId: item.id,
      },
    });
  }

  goPage(item: menuItem) {
    if (item.type === this.menuType.Folder) {
      return;
    }

    // 處理 'HyperLink' 類型
    if (item.type === this.menuType.HyperLink) {
      if (item.meta) {
        location.href = item.meta;
      }
      return;
    }

    this.router.navigate([`/${item.type}/`], {
      queryParams: {
        menuItemId: item.id,
      },
    });
  }

  getSubMenu(item: menuItem) {
    this.menuItemService.getMenuItem(item.id).subscribe({
      next: (resp: menuItem) => {
        this.menuSubList = resp.inverseParent;
      },
      error: () => {},
    });
  }

  checkDevice() {
    const isDesktop = window.innerWidth >= 1024; // 你定義的桌機寬度門檻
    if (isDesktop) {
      this.isPhoneMenuOpenStatus = false; // 強制關閉手機選單
    }
  }

  changeLang() {
    this.lang = this.lang === 'zh' ? 'en' : 'zh';
    sessionStorage.setItem('lang', this.lang);
    this.translate.use(this.lang);
    this.getMenu();
  }
}
