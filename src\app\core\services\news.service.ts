import { MenuItem } from './../../shared/models/menu.model';
import { newsListReq, resModel } from './../../shared/models/news.model';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { PagingOfNews } from '../../shared/models/pagingOfNews.model';
import { Observable } from 'rxjs';
import { palaSportDatas } from '../../shared/models/sport.model';
import {
  getNewsListReq,
  getNewsListResp,
  getNewsResp,
} from '../interface/news.interface';
import { ShareService } from './share.service';

@Injectable({
  providedIn: 'root',
})
export class NewsService {
  constructor(private http: HttpClient, private shareService: ShareService) {}

  getNewsList(req: getNewsListReq): Observable<getNewsListResp> {
    return this.http.post<getNewsListResp>('api/New_News/New_NewsList', req);
  }

  getNews(typeGroupId: string): Observable<getNewsResp> {
    return this.http.get<getNewsResp>('api/New_News/GetNew_NewsData', {
      params: {
        typeGroupId: typeGroupId,
        lang: this.shareService.getLang(),
      },
    });
  }

  getNewsForView(typeGroupId: string): Observable<getNewsResp> {
    return this.http.get<getNewsResp>('api/New_News/GetNew_NewsDataForView', {
      params: {
        typeGroupId: typeGroupId,
        lang: this.shareService.getLang(),
      },
    });
  }
}
