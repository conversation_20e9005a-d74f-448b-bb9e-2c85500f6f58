import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ShareService } from './share.service';
import { Observable } from 'rxjs';
import { getHtemlDataResp } from '../interface/html.interface';

@Injectable({
  providedIn: 'root',
})
export class HtmlService {
  constructor(private http: HttpClient, private shareService: ShareService) {}

  getHtemlData(menuitemId: string): Observable<getHtemlDataResp> {
    return this.http.get<getHtemlDataResp>('api/Html/GetHtmlData', {
      params: {
        new_menuitemId: menuitemId,
        lang: this.shareService.getLang(),
      },
    });
  }
}
