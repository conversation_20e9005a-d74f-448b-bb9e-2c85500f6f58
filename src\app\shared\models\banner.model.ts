export interface Banner {
  id?: string;
  webSiteId?: string;
  bannerDataId?: string;
  sort?: number;
  type?: string;
  enable?: boolean;
  meta?: string;
  url?: string;
  bannerContent: string;
  bannerMobileDataId: string;
  mobileURL: string;
  pcUrl: string;
  uploadType: string;

}

export interface Link {
  id: string;
  name: string;
  url: string;
  coverDataId: string;
  sort: number;
  onHomepage: boolean;
  enable: boolean;
  menuItemId: string;
  meta: string;
  coverUrl: string;
}
