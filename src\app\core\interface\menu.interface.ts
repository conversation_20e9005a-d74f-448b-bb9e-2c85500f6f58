import { defaultItem } from './share.interface';

export interface getMenuListResp extends defaultItem {
  data: menuItem[];
}

export interface menuItem {
  id: string;
  parentId?: string;
  webSiteId?: string;
  name?: string;
  enName?: string;
  type?: string;
  meta?: string;
  sort?: number;
  visibility?: string;
  onSiteMap?: boolean;
  moreItemText?: string;
  iconDataId?: string;
  linkMenuItemId?: string;
  hiddenTitle?: boolean;
  hiddenPageTitle?: boolean;
  bannerLdataId?: string;
  bannerMdataId?: string;
  bannerSdataId?: string;
  useChildrenBanner?: boolean;
  path?: string[];
  iconUrl?: string;
  bannerLUrl?: string;
  bannerMUrl?: string;
  bannerSUrl?: string;
  editable?: boolean;
  inverseParent?: menuItem[];
  isMenuOpen?: boolean;
}
