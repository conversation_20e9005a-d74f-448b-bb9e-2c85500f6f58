<main class="main-layout">
    <div class="pages-layout">
        <h2 class="pages-title">{{title}}</h2>
        <!--內容放置區-->
        <div class="pages-content">
            <div class="pages-content-lsit">
                <!--查詢條件-->
                <div class="form-module-layout" [formGroup]="form">
                    <div class="form-module-item form-module-item-full">
                        <p class="form-module-title"><span class="form-module-title-imp"></span>訂閱服務</p>
                        <!-- 下拉選單元件 START-->
                        <div class="form-module-item">
                            <div>
                                <span class="radio-list">
                                    <input id="radio1" type="radio" name="subscribe" [value]="1" (change)="getCaptcha()"
                                        formControlName="subscribe" />
                                    <label for="radio1">訂閱</label>
                                </span>
                                <span class="radio-list">
                                    <input id="radio2" type="radio" name="subscribe" [value]="2" (change)="getCaptcha()"
                                        formControlName="subscribe" />
                                    <label for="radio2">取消訂閱</label>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="form-module-item">
                        <p class="form-module-title"><span class="form-module-title-imp"></span>電子信箱
                        </p>
                        <div class="module-input">
                            <input type="search" class="form-control w-full" placeholder="請輸入Email" title="請輸入Email"
                                formControlName="email">
                        </div>
                    </div>
                    <div class="form-module-item">
                        <p class="form-module-title"><span class="form-module-title-imp"></span>驗證碼
                        </p>

                        <div class="form-module-item-list">
                            <div class="verification-code">
                                <!--輸入框-->
                                <input type="text" class="form-control w-full" placeholder="請輸入驗證碼" title="請輸入驗證碼"
                                    formControlName="reCaptcha">&nbsp;&nbsp;
                                <!--驗證圖片-->
                                <span class="verification-code-img"><img [src]="captchaImage" alt="驗證圖片"></span>
                                <!--重整按鈕-->
                                <button class="btn-list btn-primary-color" (click)="getCaptcha()">
                                    <span class="material-symbols-outlined">autorenew</span>
                                    <span class="verification-code-title">重新取得驗證碼</span>
                                </button>
                            </div>
                            <input class="btn-list btn-default-color" value="取消" type="button">
                            <input class="btn-list btn-primary-color" (click)="send()" value="確定" type="button">
                        </div>

                    </div>

                </div>
                <!--列表-->
                <div class="news-list-layout">
                    <div class="news-list-cont">
                        @for (item of newsList; track item) {
                        <article class="news-list-cont-item" (click)="goENewsLetter(item.typeGroupId)">
                            <div class="news-list-cont-item-list">
                                <span className="news-list-cont-item-date">{{item.e_NewsletterPublishDateTime|date:
                                    'yyyy.MM.dd'}}</span>
                                <h3 class="news-list-cont-item-title">{{item.name}}</h3>
                            </div>
                        </article>
                        }@empty {
                        <div class="not-found">
                            <span>查無資料</span>
                        </div>
                        }
                    </div>
                </div>
                @if(newsList.length > 0){
                <app-paginator [pageSize]="pageSize" [nowPage]="nowPage" [totalRecords]="totalCount"
                    [pageShowCount]="pageShowCount" currentPageReportTemplate="第 {first} 到 {last} 筆，共 {totalRecords} 筆"
                    (clickPageEvent)="getPageFromPaginator($event)"
                    (pageSizeChangeEvent)="getPageSizeFromPaginator($event)"></app-paginator>
                }
            </div>
        </div>

    </div>

</main>