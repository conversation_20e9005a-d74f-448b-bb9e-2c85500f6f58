import { defaultItem } from './share.interface';

export interface getShareResourceListReq {
  menuitemId: string;
  lang: string;
  currentPage: number;
  pageSize: number;
  keyword?: string;
  type?: string;
}
export interface getShareResourceListResp extends defaultItem {
  data: {
    title: string;
    totalCount: number;
    totalPage: number;
    data: shareResourceItem[];
  };
}

export interface shareResourceItem {
  shareResourceId: string;
  sportType: string;
  name: string;
  fileId: string;
  fileName: string;
  fileUrl: string;
  fileType: string;
}
