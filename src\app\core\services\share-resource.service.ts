import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import {
  getShareResourceListReq,
  getShareResourceListResp,
} from '../interface/shareResource.interface';

@Injectable({
  providedIn: 'root',
})
export class ShareResourceService {
  constructor(private httpClient: HttpClient) {}

  getShareResourceList(
    req: getShareResourceListReq
  ): Observable<getShareResourceListResp> {
    return this.httpClient.get<getShareResourceListResp>(
      'api/ShareResource/GetShareResourceList',
      {
        params: {
          ...req,
        },
      }
    );
  }
}
