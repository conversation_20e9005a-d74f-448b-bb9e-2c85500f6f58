import { Component, Input, OnInit } from '@angular/core';
import { getHtemlDataResp } from '../../../core/interface/html.interface';
import { HtmlService } from '../../../core/services/html.service';

@Component({
  selector: 'app-tab-html',
  standalone: false,

  templateUrl: './tab-html.component.html',
  styleUrl: './tab-html.component.scss',
})
export class TabHtmlComponent implements OnInit {
  @Input() menuItemId: string = '';
  content: string = '';

  constructor(private htmlService: HtmlService) {}

  ngOnInit() {
    this.getContent();
  }

  getContent() {
    this.htmlService.getHtemlData(this.menuItemId).subscribe({
      next: (resp: getHtemlDataResp) => {
        this.content = resp.data.content;
      },
      error: () => {},
    });
  }
}
