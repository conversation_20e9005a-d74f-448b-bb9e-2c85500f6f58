// Scss Document

.faq-card-layout {
  max-width: 1200px;
	width: 100%;
  margin: 0 auto;
	line-height: 1.6;

  .faq-card-item {
    border-bottom: 1px solid #ddd;
    padding: 1rem 0;

    &-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: pointer;
      h3 {
        margin: 0;
      }
    }

    &-cont {
      max-height: 0;
      overflow: hidden;
      transition: max-height 0.3s ease, opacity 0.3s ease;
      opacity: 0;
      &-text {
        padding-top: 0.5rem;
      }
    }

    .material-symbols-outlined {
      transition: transform 0.3s ease;
    }

    &.active {
      .material-symbols-outlined {
        transform: rotate(180deg);
      }

      .faq-card-item-cont {
        opacity: 1;
      }
    }
  }
}
