﻿import { MenuItem } from "./menu.model";

export interface PagingOfNews {
  newType: number;
  paging: {
    currentPageIndex?: number;
    hasNextPage?: boolean;
    hasPreviousPage?: boolean;
    result?: News[];
    skip?: number;
    take?: number;
    totalCount?: number;
    totalPageCount?: number;
  }
}

export interface News {
  id?: string;
  title?: string;
  startTime?: number;
  endTime?: number;
  enable?: boolean;
  isTop?: boolean;
  coverDataId?: string;
  authorName?: string;
  menuItemId?: string;
  description?: string;
  meta?: string;
  coverAlt?: string;
  combineNews?: CombineNews[];
  newsContent?: NewsContent[];
  newsDocument?: NewsDocument[];
  news_R_Type?: [];
  coverUrl?: string;
  time?: number;
  top1?: boolean;
  viewCount?: number;
  timeShow: boolean;
}

export class CombineNews {
  id?: string;
  menuItemId?: string;
  newsId?: string;
  menuItem?: MenuItem;
  webSite?: WebSite;
}

export class WebSite {
  id?: string;
  name?: string;
  creater?: string;
  sort?: number;
  enable?: boolean;
  alias?: string;
  cacheTick?: number;
}

export class NewsContent {
  id?: string;
  newsId?: string;
  title?: string;
  content?: string;
  sort?: number;
}

export class NewsDocument {
  id?: string;
  newsId?: string;
  fileName?: string;
  sort?: number;
  size?: number;
  documentDataId?: string;
  documentUrl?: string;
}
