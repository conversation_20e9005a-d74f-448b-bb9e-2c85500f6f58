import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

export const EVENT_SHOW_LOADING = 'EVENT_SHOW_LOADING';
export const EVENT_CLOSE_LOADING = 'EVENT_CLOSE_LOADING';
@Injectable({
  providedIn: 'root'
})
export class LoadingService {
  loadingSubject = new BehaviorSubject<boolean>(false);
  loading$ = this.loadingSubject.asObservable();

  constructor() { }

  public static dispatchEvent(event: string) {
    window.dispatchEvent(new CustomEvent(event));
  }

  public show() {
    // alert('show loading')
    this.loadingSubject.next(true);
  }

  public close() {
    this.loadingSubject.next(false);
  }
}
