import { Component, OnInit } from '@angular/core';
import {
  getLineListReq,
  getLineListResp,
  lineItem,
} from '../../../core/interface/line.interface';
import { ActivatedRoute, Router } from '@angular/router';
import { LineService } from '../../../core/services/line.service';
import { ShareService } from '../../../core/services/share.service';

@Component({
  selector: 'app-line-list',
  standalone: false,

  templateUrl: './line-list.component.html',
  styleUrl: './line-list.component.scss',
})
export class LineListComponent implements OnInit {
  menuItemId: string = '';
  title: string = '';
  lineList: lineItem[] = [];
  nowPage: number = 1;
  pageSize: number = 10;
  pageShowCount: number = 5; //分頁器秀幾個
  totalPage: number = 0;
  totalCount: number = 0;
  constructor(
    private lineService: LineService,
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private shareService: ShareService
  ) {
    this.activatedRoute.queryParamMap.subscribe((params) => {
      this.menuItemId = params.get('menuItemId')!;
    });
  }
  ngOnInit() {
    this.getLineList();
  }
  getLineList() {
    let req: getLineListReq = {
      menuitemId: this.menuItemId,
      currentPage: this.nowPage,
      pageSize: this.pageSize,
      lang: this.shareService.getLang(),
    };
    this.lineService.getLineList(req).subscribe({
      next: (resp: getLineListResp) => {
        this.title= resp.data.title;
        this.totalCount = resp.data.totalCount;
        this.totalPage = resp.data.totalPage;
        this.lineList = resp.data.data;
      },
      error: () => {},
    });
  }

  /**
   * 第幾頁
   * @param item number
   */
  getPageFromPaginator(nowPage: number) {
    this.nowPage = nowPage;
    this.getLineList();
  }
  /**
   * 每頁資料數量
   * @param item number
   */
  getPageSizeFromPaginator(pageSize: number) {
    this.pageSize = pageSize;
    this.nowPage = 1;
    this.getLineList();
  }

  goLine(typeGroupId: string) {
    this.router.navigate(['/LineDetail'], {
      queryParams: { typeGroupId: typeGroupId },
    });
  }
}
