import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import {
  getEbookListReq,
  getEbookListResp,
  getEbookResp,
} from '../interface/ebook.interface';

@Injectable({
  providedIn: 'root',
})
export class EbookService {
  constructor(private httpClient: HttpClient) {}

  getEbookList(req: getEbookListReq): Observable<getEbookListResp> {
    return this.httpClient.post<getEbookListResp>('api/Ebook/EbookList', req);
  }

  getEbook(id: string): Observable<getEbookResp> {
    return this.httpClient.get<getEbookResp>(`api/Ebook/${id}`);
  }
}
