@use "./scss/footer-layout.scss";

// @media print {
//     .cookie-bar {
//         display: none !important;
//     }
// }

// .cookie-bar {
//     position: fixed;
//     bottom: 0;
//     left: 20px;
//     right: 20px;
//     z-index: 201;
//     transform: translateY(105%);
//     transition: transform 0.5s ease;
//     pointer-events: none;
// }
// .cookie-bar.is-cloaked {
//     transition: none;
// }
// .cookie-bar.is-active {
//     transform: translateY(0);
// }
// .cookie-bar-content {
//     pointer-events: auto;
//     box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.251);
//     width: 1360px;
//     max-width: 100%;
//     background: #fff;
//     margin-left: auto;
//     margin-right: auto;
//     margin-bottom: 20px;
// }
