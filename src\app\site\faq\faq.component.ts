import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { FaqService } from '../../core/services/FaqService';
import { ShareService } from '../../core/services/share.service';
import {
  faqItem,
  getFaqListReq,
  getFaqListResp,
} from '../../core/interface/faq.interface';
import {
  getTypeListResp,
  typeItem,
} from '../../core/interface/share.interface';
import { FormBuilder, FormGroup } from '@angular/forms';
import { SpinnerService } from '../../core/utils/spinner.service';

@Component({
  selector: 'app-faq',
  standalone: false,

  templateUrl: './faq.component.html',
  styleUrl: './faq.component.scss',
})
export class FaqComponent implements OnInit {
  form: FormGroup;
  menuItemId: string = '';
  isPreview: boolean = false;
  title: string = '';
  selectedFaqId: string = '';

  typeList: typeItem[] = [];
  keyWord: string = '';
  typeGroupId: string = '';
  faqList: faqItem[] = [];
  nowPage: number = 1;
  pageSize: number = 10;
  pageShowCount: number = 5; //分頁器秀幾個
  totalPage: number = 0;
  totalCount: number = 0;

  constructor(
    private faqService: FaqService,
    private activatedRoute: ActivatedRoute,
    private shareService: ShareService,
    private fb: FormBuilder,
    private spinnerService: SpinnerService
  ) {
    this.activatedRoute.queryParamMap.subscribe((params) => {
      this.menuItemId = params.get('menuItemId')!;
      this.isPreview = (params.get('isPreview') as string) == 'true';
      this.typeGroupId = params.get('typeGroupId') as string;
    });
    this.form = this.fb.group({
      // type: [''],
      keyWord: [''],
    });
  }

  ngOnInit() {
    this.getTypeList();
    if (this.isPreview) {
      this.getFaqListForView();
    }
    this.getFaqList();
  }

  getTypeList() {
    this.shareService.getTypeList('問題分類').subscribe({
      next: (resp: getTypeListResp) => {
        this.typeList = resp.data;
      },
      error: () => {},
    });
  }

  search() {
    this.nowPage = 1;
    this.getFaqList();
  }

  getFaqListForView() {
    this.spinnerService.show();
    this.faqService.getFaqListForView(this.typeGroupId).subscribe({
      next: (resp: getFaqListResp) => {
        this.spinnerService.hide();
        this.title = resp.data.title;
        this.totalCount = resp.data.totalCount;
        this.totalPage = resp.data.totalPage;
        this.faqList = resp.data.data;
        if (this.faqList.length > 0) {
          this.selectedFaqId = this.faqList[0].faqId;
        }
      },
      error: () => {
        this.spinnerService.hide();
      },
    });
  }

  getFaqList() {
    let req: getFaqListReq = {
      menuitemId: this.menuItemId,
      currentPage: this.nowPage,
      pageSize: this.pageSize,
      type: '',
      keyword: this.form.value.keyWord,
      lang: this.shareService.getLang(),
    };
    this.spinnerService.show();
    this.faqService.getFaqList(req).subscribe({
      next: (resp: getFaqListResp) => {
        this.spinnerService.hide();
        this.title = resp.data.title;
        this.totalCount = resp.data.totalCount;
        this.totalPage = resp.data.totalPage;
        this.faqList = resp.data.data;
        if (this.faqList.length > 0) {
          this.selectedFaqId = this.faqList[0].faqId;
        }
      },
      error: () => {
        this.spinnerService.hide();
      },
    });
  }

  toggleFaq(faqId: string) {
    this.selectedFaqId = this.selectedFaqId === faqId ? '' : faqId;
  }

  /**
   * 第幾頁
   * @param item number
   */
  getPageFromPaginator(nowPage: number) {
    this.nowPage = nowPage;
    this.getFaqList();
  }
  /**
   * 每頁資料數量
   * @param item number
   */
  getPageSizeFromPaginator(pageSize: number) {
    this.pageSize = pageSize;
    this.nowPage = 1;
    this.getFaqList();
  }
}
