<main class="main-layout">
    <div class="pages-layout">
        <h2 class="pages-title">{{title}}</h2>
        <!--內容放置區-->
        <div class="pages-content">
            <!--內容放置區-->
            <div class="ebook-list-layout">
                @for (item of ebookList; track item) {
                <div class="ebook-list-item">
                    <a href="" [title]="item.title" [routerLink]="['/EbookDetail']"
                        [queryParams]="{bookId: item.bookId}">
                        <div class="ebook-list-item-img">
                            <img [src]="item.coverUrl" alt="">
                        </div>
                        <div class="ebook-list-item-cont">
                            <p class="ebook-list-item-cont-title">{{item.title}}</p>
                            <p class="ebook-list-item-cont-text">{{item.littleTile}}</p>
                            <p class="ebook-list-item-cont-text">{{item.littleTitleZh}}</p>
                            <p class="ebook-list-item-cont-text">{{item.period}}</p>
                        </div>
                    </a>
                </div>
                }
            </div>
            @if(ebookList.length > 0){
            <app-paginator [pageSize]="pageSize" [nowPage]="nowPage" [totalRecords]="totalCount"
                [pageShowCount]="pageShowCount" currentPageReportTemplate="第 {first} 到 {last} 筆，共 {totalRecords} 筆"
                (clickPageEvent)="getPageFromPaginator($event)"
                (pageSizeChangeEvent)="getPageSizeFromPaginator($event)"></app-paginator>
            }@else{
            <div class="not-found">
                <span>查無資料</span>
            </div>
            }
        </div>
    </div>

</main>