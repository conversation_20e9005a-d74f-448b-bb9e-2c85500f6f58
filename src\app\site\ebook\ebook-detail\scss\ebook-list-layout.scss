// Scss Document
//列表
.ebook-list{
	&-layout{
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		grid-gap: 20px;
		max-width: 1200px;
		min-width: 414px;
		a{
			display: block;
			overflow: hidden;
			text-decoration: none;
		}	
	}
	&-item{
		margin: 15px;
		padding: 0px;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: flex-start;
		box-shadow: 2px 2px 15px rgba(0, 0, 0, 0.1); 
		border-radius: 10px;
		overflow: hidden;
		box-sizing: border-box;
		&-img{
			img{
				display: block;
				width: 100%;
				height: auto;
			}
		}
		&-cont{
			padding: 20px;
			color: #000;
			line-height: 1.6;
			&-title{
				margin: 0;
				font-size: 1.5em;
				font-weight: bold;
			}
			&-text{
				margin: 0;
				padding: 5px 0 0;
				font-size: 1.125em;
			}
		}
	}
}

.ebook-cont{
	&-layout{
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		justify-content: space-between;
		max-width: 1200px;
	}
	&-infor{
		margin: 0 0 30px;
		padding: 10px;
		display: flex;
		flex-direction: row;
		align-items: flex-start;
		justify-content: space-between;
		gap: 1.5em;
		p{
			margin: 5px 0;
		}
		.ebook-list-item-img{
			img{
				max-width: 300px;
			}
		}
	}
	&-text{
		display: flex;
		flex-direction: row;
		align-items: flex-start;
		justify-content: space-between;
		gap: 1.5em;
	}
}