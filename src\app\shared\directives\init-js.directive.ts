import { Directive, Input, SimpleChanges } from '@angular/core';

declare var initTabbarSwiper: any;
declare var toggleMenu: any;
declare var headerFunction: any;
declare var initAutoSwiper: any;
declare var initAlbumSwiper: any;
declare var initBannerSwiper: any;
declare var initCarouselSwiper: any;
declare var initCardSwiper: any;
declare var initHistorySwiper: any;
declare var tabToggleMethod: any;
declare var initPokerSwiper: any;

@Directive({
  selector: '[appInitJs]',
  standalone: false,
})
export class InitJsDirective {
  funcType: string = '';
  @Input() set appInitJs(dataObj: any) {
    this.funcType = dataObj.funcType;
  }

  constructor() {}

  ngAfterViewInit(): void {
    switch (this.funcType) {
      case 'Tabbar':
        initTabbarSwiper();
        break;
      case 'Header':
        toggleMenu();
        headerFunction();
        break;
      case 'Album':
        initAutoSwiper();
        initAlbumSwiper();
        break;
      case 'Carousel':
        initBannerSwiper();
        initCarouselSwiper();
        break;
      case 'Card':
        initCardSwiper();
        break;
      case 'HtmlSection':
        initHistorySwiper();
        tabToggleMethod();
        initPokerSwiper();
        initAlbumSwiper();
        break;
    }
  }
}
