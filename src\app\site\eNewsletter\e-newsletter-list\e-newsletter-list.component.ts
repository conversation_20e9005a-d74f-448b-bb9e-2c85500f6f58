import { Component, OnInit } from '@angular/core';
import { ENewsletterService } from '../../../core/services/enewsletter.service';
import { ShareService } from '../../../core/services/share.service';
import {
  eNewsLetterItem,
  getENewsLetterListResp,
  subscribeReq,
  unsubscribeReq,
} from '../../../core/interface/enewsletter.interface';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { SafeUrl } from '@angular/platform-browser';
import { HttpErrorResponse } from '@angular/common/http';
import { ActivatedRoute, Router } from '@angular/router';
import Swal from 'sweetalert2';
import { SpinnerService } from '../../../core/utils/spinner.service';
import { defaultItem } from '../../../core/interface/share.interface';

@Component({
  selector: 'app-e-newsletter-list',
  standalone: false,

  templateUrl: './e-newsletter-list.component.html',
  styleUrl: './e-newsletter-list.component.scss',
})
export class ENewsletterListComponent implements OnInit {
  form: FormGroup;
  title: string = '';
  pageSize: number = 10;
  nowPage: number = 1;
  totalCount: number = 0;
  pageShowCount: number = 10; //分頁器秀幾個
  menuItemId: string = '';

  newsList: eNewsLetterItem[] = [];
  captchaImage!: SafeUrl;
  constructor(
    private router: Router,
    private formBuilder: FormBuilder,
    private shareService: ShareService,
    private eNewsletterService: ENewsletterService,
    private activatedRoute: ActivatedRoute,
    private spinnerService: SpinnerService
  ) {
    this.activatedRoute.queryParamMap.subscribe((params) => {
      this.menuItemId = params.get('menuItemId')!;
      if (params.get('token') && params.get('status')) {
        params.get('status') == '1'
          ? this.checkSubscribe(params.get('token') as string)
          : this.checkUnSubscribe(params.get('token') as string);
      }
    });
    this.form = this.formBuilder.group({
      subscribe: [1],
      email: ['', Validators.required],
      reCaptcha: ['', Validators.required],
    });
  }

  ngOnInit() {
    this.getENewsLetterList();
    this.getCaptcha();
  }

  getENewsLetterList() {
    this.eNewsletterService
      .getENewsLetterList(this.nowPage, this.pageSize)
      .subscribe({
        next: (resp: getENewsLetterListResp) => {
          this.title = resp.data.title;
          this.totalCount = resp.data.totalCount;
          this.newsList = resp.data.data;
        },
        error: () => {},
      });
  }

  /**
   * 第幾頁
   * @param item number
   */
  getPageFromPaginator(nowPage: number) {
    this.nowPage = nowPage;
    this.getENewsLetterList();
  }
  /**
   * 每頁資料數量
   * @param item number
   */
  getPageSizeFromPaginator(pageSize: number) {
    this.pageSize = pageSize;
    this.nowPage = 1;
    this.getENewsLetterList();
  }

  getCaptcha() {
    this.eNewsletterService.getCaptcha().subscribe({
      next: (resp: Blob) => {
        this.captchaImage = this.shareService.getSafeImageUrl(resp);
      },
      error: (err: HttpErrorResponse) => {},
    });
  }

  goENewsLetter(typeGroupId: string) {
    this.router.navigate(['ENewsletterDetail'], {
      queryParams: { typeGroupId: typeGroupId },
    });
  }

  checkSubscribe(token: string) {
    this.eNewsletterService.checkSubscribe(token).subscribe({
      next: (resp: any) => {
        if (resp.code === 200) {
          Swal.fire({
            title: '訂閱完成',
            html: `
        <div style="display: flex; justify-content: center; align-items: center;">
          <div style="display: flex; flex-direction: column; gap: 10px; text-align: left;">
           <span>感謝您的訂閱</span>
           <span>電子信箱：${resp.data.email}</span>
           <span>已正式確認訂閱完成。</span>
          </div>
        </div>`,
            icon: 'success',
            showCancelButton: false,
            confirmButtonText: '確認',
            reverseButtons: true,
          }).then(() => {
            this.router.navigate([], {
              queryParams: {
                menuItemId: this.menuItemId,
              },
              replaceUrl: true,
            });
          });
        } else {
          Swal.fire('失敗', resp.message, 'error').then(() => {
            this.router.navigate([], {
              queryParams: {
                menuItemId: this.menuItemId,
              },
              replaceUrl: true,
            });
          });
        }
      },
      error: (err: HttpErrorResponse) => {
        Swal.fire('失敗', err.error.message, 'error').then(() => {
          this.router.navigate([], {
            queryParams: {
              menuItemId: this.menuItemId,
            },
            replaceUrl: true,
          });
        });
      },
    });
  }

  checkUnSubscribe(token: string) {
    this.eNewsletterService.checkUnsubscribe(token).subscribe({
      next: (resp: any) => {
        if (resp.code === 200) {
          Swal.fire({
            title: '取消訂閱完成',
            html: `
        <div style="display: flex; justify-content: center; align-items: center;">
          <div style="display: flex; flex-direction: column; gap: 10px; text-align: left;">
           <span>感謝您的訂閱</span>
           <span>電子信箱：${resp.data.email}</span>
           <span>已正式確認取消訂閱完成。</span>
          </div>
        </div>`,
            icon: 'success',
            showCancelButton: false,
            confirmButtonText: '確認',
            reverseButtons: true,
          }).then(() => {
            this.router.navigate([], {
              queryParams: {
                menuItemId: this.menuItemId,
              },
              replaceUrl: true,
            });
          });
        } else {
          Swal.fire('失敗', resp.message, 'error').then(() => {
            this.router.navigate([], {
              queryParams: {
                menuItemId: this.menuItemId,
              },
              replaceUrl: true,
            });
          });
        }
      },
      error: (err: HttpErrorResponse) => {
        Swal.fire('失敗', err.error.message, 'error').then(() => {
          this.router.navigate([], {
            queryParams: {
              menuItemId: this.menuItemId,
            },
            replaceUrl: true,
          });
        });
      },
    });
  }

  send() {
    this.form.value.subscribe === 1 ? this.subscribe() : this.unsubscribe();
  }

  subscribe() {
    if (this.form.invalid) {
      Swal.fire('警告', '請填寫電子郵件和驗證碼', 'error');
      return;
    }
    let req: subscribeReq = {
      email: this.form.value.email,
      reCaptcha: this.form.value.reCaptcha,
    };
    this.spinnerService.show();
    this.eNewsletterService.subscribe(req).subscribe({
      next: (resp: defaultItem) => {
        this.spinnerService.hide();
        if (resp.code === 200) {
          Swal.fire('成功', '寄信成功，請去信箱收信確認訂閱', 'success').then(
            () => {
              this.form.reset();
              this.getCaptcha();
            }
          );
        } else {
          Swal.fire('失敗', resp.message, 'error').then(() => {
            this.getCaptcha();
          });
        }
      },
      error: (err: HttpErrorResponse) => {
        this.spinnerService.hide();
        Swal.fire('失敗', err.error.message, 'error').then(() => {
          this.getCaptcha();
        });
      },
    });
  }

  unsubscribe() {
    if (this.form.invalid) {
      Swal.fire('警告', '請填寫電子郵件和驗證碼', 'error');
      return;
    }
    let req: unsubscribeReq = {
      email: this.form.value.email,
      reCaptcha: this.form.value.reCaptcha,
    };
    this.spinnerService.show();
    this.eNewsletterService.unsubscribe(req).subscribe({
      next: (resp: any) => {
        if (resp.code === 200) {
          this.spinnerService.hide();
          Swal.fire(
            '成功',
            '寄信成功，請去信箱收信確認訂閱取消',
            'success'
          ).then(() => {
            this.form.reset();
            this.getCaptcha();
          });
        } else {
          Swal.fire('失敗', resp.message, 'error').then(() => {
            this.getCaptcha();
          });
        }
      },
      error: (err: HttpErrorResponse) => {
        this.spinnerService.hide();
        Swal.fire('失敗', err.error.message, 'error').then(() => {
          this.getCaptcha();
        });
      },
    });
  }
}
