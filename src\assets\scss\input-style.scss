// Scss Document
button,
input,
select,
textarea {
	box-sizing: border-box;
	font-size: 1.125em;
	font-family: Helvetica, Arial, "微軟正黑體", sans-serif;
}
select {
	margin: 0;
	padding: 13px 10px;
	border-radius: 5px;
	background-color: #fff;
	border: 1px solid #ccc;
	line-height: 1.42857;
	width: 100%;
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset;
	transition:
		border-color 0.15s ease-in-out 0s,
		box-shadow 0.15s ease-in-out 0s;
	&:focus-visible {
		outline: 0;
		box-shadow: 0 0 0 0.25rem #177691;
	}
}

//輸入
.form-control {
	margin: 0;
	padding: 10px;
	background-color: #fff;
	background-image: none;
	border: 1px solid #ccc;
	border-radius: 5px;
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset;
	display: inline-block;
	line-height: 1.42857;
	transition:
		border-color 0.15s ease-in-out 0s,
		box-shadow 0.15s ease-in-out 0s;
	&:focus {
		outline: 0;
		box-shadow: 0 0 0 0.25rem #177691;
	}
	&[disabled] {
		border-color: #65676b;
		background-color: #e7e8e9;
		color: #333;
		cursor: default;
	}
}
//下拉
.select_control {
	margin: 2px;
	padding: 0;
	overflow: hidden;
	select {
		border: 1px solid #ccc;
		border-radius: 5px;
		padding: 10px 16px 10px 10px;
		margin: 0;
		> option {
			border-bottom: 1px solid #ccc;
			padding: 4px;
		}
	}
}
//驗證
.verification-code {
	display: flex;
	&-title {
		display: none;
	}
	&-img {
		display: inline-block;
		margin: 0;
		padding: 0;
		line-height: 0;
	}
	.btn-list {
		padding: 5px 10px;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		border-radius: 0 5px 5px 0;
	}
	.form-control {
		border-radius: 5px 0 0 5px;
	}
}

@media screen and (max-width: 768px) {
	button,
	textarea {
		width: 100%;
	}
	.select_control {
		select {
			margin: 8px 0;
			width: 100% !important;
		}
	}
	.verification-code {
		.btn-list {
			width: auto;
		}
	}
}

@media screen and (max-width: 414px) {
	.verification-code {
		flex-direction: column;
		&-title {
			display: none;
		}
		&-img {
			img {
				width: 100%;
			}
		}
		.btn-list {
			border-radius: 0 0 5px 5px;
		}
		.form-control {
			border-radius: 5px 5px 0 0;
		}
	}
}
