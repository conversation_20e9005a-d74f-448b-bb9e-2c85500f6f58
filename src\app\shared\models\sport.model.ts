export interface sportResModel {
  message: string;
  data: sportDataModel;
}

export interface sportDataModel {
  sportDataId: string;
  title: string;
  sportSchdules: sportShareResource[];
  sportDataShares: sportShareResource[];
  fbUrl: string;
  sportType: string;
  imageDataId: string;
  imageUrl: string;
  youtubeUrl: string;
  sportDatas: sportDataItem[];
  palaSportDatas?: sportDataItem[];
  sportPlaceMenuitenId: string;
  newsMenuitemId: string;
  freeTicket: string;
}

export interface sportShareResource {
  sportShareResourceId: string;
  sportShareResourceName: string;
  fileUrl: string;
}

export interface sportDataItem {
  sportClass: string;
  age: string;
  sportType: string;
  sportName: string;
}

export interface palaSportDatas {
  fileMessage: string;
  sportPlaceDate: string;
  sportTableDatas: sportTableDataItem[];
}

export interface sportTableDataItem {
  sportClass: string;
  ageData: string;
}
