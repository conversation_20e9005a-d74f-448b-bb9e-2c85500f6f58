import { Component } from '@angular/core';
import { dataItem } from '../../../core/interface/type.interface';
import { ActivatedRoute } from '@angular/router';
import { getNewsResp } from '../../../core/interface/news.interface';
import { ImageService } from '../../../core/services/image.service';
import { SpinnerService } from '../../../core/utils/spinner.service';

@Component({
  selector: 'app-image',
  standalone: false,

  templateUrl: './image.component.html',
  styleUrl: './image.component.scss',
})
export class ImageComponent {
  contentData: dataItem[] = [];
  isPreview: boolean = false;
  typeGroupId: string = '';
  title: string = '';
  constructor(
    private activatedRoute: ActivatedRoute,
    private imageService: ImageService,
    private spinnerService: SpinnerService
  ) {
    this.activatedRoute.queryParamMap.subscribe((params) => {
      this.isPreview = (params.get('isPreview') as string) == 'true';
      if (this.isPreview) {
        this.getImageForView(params.get('typeGroupId')!);
      } else {
        this.getImage(params.get('typeGroupId')!);
      }
    });
  }
  getImageForView(typeGroupId: string) {
    this.spinnerService.show();
    this.imageService.getImageForView(typeGroupId).subscribe({
      next: (resp: getNewsResp) => {
        this.spinnerService.hide();
        this.title = resp.data.title;
        this.contentData = resp.data.new_NewsTagDatas;
      },
      error: () => {
        this.spinnerService.hide();
      },
    });
  }

  getImage(typeGroupId: string) {
    this.spinnerService.show();
    this.imageService.getImage(typeGroupId).subscribe({
      next: (resp: getNewsResp) => {
        this.spinnerService.hide();
        this.title = resp.data.title;
        this.contentData = resp.data.new_NewsTagDatas;
      },
      error: () => {
        this.spinnerService.hide();
      },
    });
  }
}
