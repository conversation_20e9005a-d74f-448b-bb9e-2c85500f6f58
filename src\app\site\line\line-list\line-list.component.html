<main class="main-layout">
    <div class="pages-layout">
        <div class="pages-title h2">{{title}}</div>
        <!--內容放置區-->
        <div class="pages-content">
            <div class="pages-content-lsit">
                <!--列表-->
                <div class="news-list-layout">
                    <div class="news-list-cont">
                        @for (item of lineList; track item) {
                        <article class="news-list-cont-item" (click)="goLine(item.typeGroupId)">
                            <div class="news-list-cont-item-list">
                                <span className="news-list-cont-item-date">{{item.startTime}}</span>
                                <div class="news-list-cont-item-title h3">{{item.title}}</div>
                            </div>
                        </article>
                        }
                    </div>
                </div>
                @if(lineList.length < 1){ <div class="not-found">
                    <span>查無資料</span>
            </div> }
            @if(lineList.length > 0){
            <app-paginator [pageSize]="pageSize" [nowPage]="nowPage" [totalRecords]="totalCount"
                [pageShowCount]="pageShowCount" currentPageReportTemplate="第 {first} 到 {last} 筆，共 {totalRecords} 筆"
                (clickPageEvent)="getPageFromPaginator($event)"
                (pageSizeChangeEvent)="getPageSizeFromPaginator($event)"></app-paginator>
            }
        </div>
    </div>
    </div>
</main>