import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { dataItem } from '../../../core/interface/type.interface';
import { LineService } from '../../../core/services/line.service';
import { getNewsResp } from '../../../core/interface/news.interface';

@Component({
  selector: 'app-line',
  standalone: false,

  templateUrl: './line.component.html',
  styleUrl: './line.component.scss',
})
export class LineComponent {
  contentData: dataItem[] = [];
  isPreview: boolean = false;
  typeGroupId: string = '';
  title: string = '';
  constructor(
    private activatedRoute: ActivatedRoute,
    private lineService: LineService
  ) {
    this.activatedRoute.queryParamMap.subscribe((params) => {
      this.isPreview = (params.get('isPreview') as string) == 'true';
      if (this.isPreview) {
        this.getLineForView(params.get('typeGroupId')!);
      } else {
        this.getLine(params.get('typeGroupId')!);
      }
    });
  }

  getLineForView(typeGroupId: string) {
    this.lineService.getLineForView(typeGroupId).subscribe({
      next: (resp: getNewsResp) => {
        this.title = resp.data.title;
        this.contentData = resp.data.new_NewsTagDatas;
      },
      error: () => {},
    });
  }

  getLine(typeGroupId: string) {
    this.lineService.getLine(typeGroupId).subscribe({
      next: (resp: getNewsResp) => {
        this.title = resp.data.title;
        this.contentData = resp.data.new_NewsTagDatas;
      },
      error: () => {},
    });
  }
}
