import { Component, Input } from '@angular/core';
import { getHtemlDataResp } from '../../../core/interface/html.interface';
import { HtmlService } from '../../../core/services/html.service';

@Component({
  selector: 'app-tab-html-zip',
  standalone: false,

  templateUrl: './tab-html-zip.component.html',
  styleUrl: './tab-html-zip.component.scss',
})
export class TabHtmlZipComponent {
  @Input() menuItemId: string = '';
  content: string = '';

  constructor(private htmlService: HtmlService) {}

  getContent() {
    this.htmlService.getHtemlData(this.menuItemId).subscribe({
      next: (resp: getHtemlDataResp) => {
        this.content = resp.data.content;
      },
      error: () => {},
    });
  }
}
