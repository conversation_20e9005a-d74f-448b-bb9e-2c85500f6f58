// @import "custom";

$gray-blue: #364250;
$dark-blue: #212a34;

$green: #177691;
$second-green: #65dec7;
$light-green: #cbeef2;

$yellow: #f5ba4b;

$gray: #949494;
$light-gray: #e7e8e9;

$black1: #232127;

$font-weight-bold-black: $black1;
$primary: $green;
$line-gray: $gray;
$disabled-gray: $light-gray;

// form
$error-red: #f0524b;

.paginator-group {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .paginator-detail {
    display: flex;
    align-items: center;
    font-size: 1em;
    color: $gray-blue;

    span {
      font-size: 1em;
    }

    select {
      border-radius: 5px;
      margin: 0 10px;
      padding: 10px;
      font-size: 1em;
      border: 1px solid rgba($gray, 1);
      width: 70px; // 調整寬度，防止過寬
    }
    input {
      border-radius: 5px;
      margin: 0 10px;
      padding: 10px;
      font-size: 1em;
      border: 1px solid rgba($gray, 1);
      width: 90px; // 調整寬度，防止過寬
    }
  }

  .paginator-page {
    display: flex;
    align-items: center;

    .page-btn {
      cursor: pointer;
      margin-right: 10px;
      border: 1px solid rgba($gray-blue, 0.2);
      border-radius: 4px;
      min-width: 25px;
      height: 25px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s;

      span {
        font-size: 1em;
        line-height: 14px;
        color: $gray-blue;
        transition: all 0.3s;
      }

      &:last-child {
        margin-right: 0;
      }

      &.actived {
        background-color: $primary;

        span {
          color: #fff;
        }
      }
    }

    .pages-box {
      margin-right: 10px;
      display: flex;
      align-items: center;
    }
  }
}

.wrap {
  flex-wrap: wrap;
}
