import { defaultItem } from './share.interface';
import { dataItem } from './type.interface';

export interface getContentDataResp extends defaultItem {
  data: {
    menuitemId: string;
    new_NewsId: string;
    tabDatas: tabItem[];
    titleName: string;
    levelDecision: number;
    currentLevel: number;
    new_NewsTagDatas: dataItem[];
    creater: string;
    editor: string;
    lang: string;
  };
}

export interface tabItem {
  tabId: string;
  tabName: string;
}
