import { Component } from '@angular/core';
import { dataItem } from '../../../core/interface/type.interface';
import { NewsService } from '../../../core/services/news.service';
import { ActivatedRoute } from '@angular/router';
import { getNewsResp } from '../../../core/interface/news.interface';
import { SpinnerService } from '../../../core/utils/spinner.service';

@Component({
  selector: 'app-news',
  standalone: false,

  templateUrl: './news.component.html',
  styleUrl: './news.component.scss',
})
export class NewsComponent {
  contentData: dataItem[] = [];
  isPreview: boolean = false;
  typeGroupId: string = '';
  title: string = '';
  constructor(
    private activatedRoute: ActivatedRoute,
    private newsService: NewsService,
    private spinnerService: SpinnerService
  ) {
    this.activatedRoute.queryParamMap.subscribe((params) => {
      this.isPreview = (params.get('isPreview') as string) == 'true';
      if (this.isPreview) {
        this.getNewsForView(params.get('typeGroupId')!);
      } else {
        this.getNews(params.get('typeGroupId')!);
      }
    });
  }

  getNewsForView(typeGroupId: string) {
    this.spinnerService.show();
    this.newsService.getNewsForView(typeGroupId).subscribe({
      next: (resp: getNewsResp) => {
        this.title = resp.data.title;
        this.contentData = resp.data.new_NewsTagDatas;
      },
      error: () => {
        this.spinnerService.hide();
      },
    });
  }

  getNews(typeGroupId: string) {
    this.spinnerService.show();
    this.newsService.getNews(typeGroupId).subscribe({
      next: (resp: getNewsResp) => {
        this.spinnerService.hide();
        this.title = resp.data.title;
        this.contentData = resp.data.new_NewsTagDatas;
      },
      error: () => {
        this.spinnerService.hide();
      },
    });
  }
}
