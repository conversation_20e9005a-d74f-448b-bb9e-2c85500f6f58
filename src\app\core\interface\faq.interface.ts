import { defaultItem } from './share.interface';
import { dataItem } from './type.interface';

export interface getFaqListReq {
  menuitemId: string;
  currentPage: number;
  pageSize: number;
  type: string;
  keyword: string;
  lang: string;
}
export interface getFaqListResp extends defaultItem {
  data: {
    menuitemId: string;
    title: string;
    totalCount: number;
    totalPage: number;
    currentPage: number;
    lang: string;
    data: faqItem[];
  };
}

export interface faqItem {
  faqId: string;
  faqType: string;
  question: string;
  createUser: string;
  createTime: string;
  editUser: string;
  editTime: string;
  answer: dataItem[];
}
