import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { EbookService } from '../../../core/services/ebook.service';
import { SpinnerService } from '../../../core/utils/spinner.service';
import { getEbookResp } from '../../../core/interface/ebook.interface';

@Component({
  selector: 'app-ebook-detail',
  standalone: false,

  templateUrl: './ebook-detail.component.html',
  styleUrl: './ebook-detail.component.scss',
})
export class EbookDetailComponent {
  ebookItem!: {
    bookId: string;
    title: string;
    littleTile: string;
    littleTitleZh: string;
    period: string;
    coverUrl: string;
    ebookPreviewUrl: string;
    introduction: string;
    outline: string;
    date: string;
    author: string;
  };
  constructor(
    private ebookService: EbookService,
    private activatedRoute: ActivatedRoute,
    private spinnerService: SpinnerService
  ) {
    this.activatedRoute.queryParamMap.subscribe((params) => {
      this.getEbook(params.get('bookId')!);
    });
  }

  getEbook(bookId: string) {
    this.spinnerService.show();
    this.ebookService.getEbook(bookId).subscribe({
      next: (resp: getEbookResp) => {
        this.spinnerService.hide();
        this.ebookItem = resp.data;
      },
      error: () => {
        this.spinnerService.hide();
      },
    });
  }

  openEbook(url: string) {
    window.open(url, '_blank');
  }
}
