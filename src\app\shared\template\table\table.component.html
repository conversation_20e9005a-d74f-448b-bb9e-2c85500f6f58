<!--01 原語會版本-->
<div class="table-list">
    <div class="item-box-table" tabindex="0">
        <div class="item-grab-tutorial" title="左右滑動瀏覽">
            <span class="material-symbols-outlined">
                swap_horiz
            </span>
            <p class="item-grab-tutorial-text">左右滑動瀏覽</p>
        </div>
        <div class="item-table">
            <div class="item-box-table-tbody grab-container" #scrollContainer>
                <div class="tbody grab-children">
                    <!-- 一列 START-->
                    <div class="tr style-full-table style-thead">
                        <div class="td td-content">
                            <div class="row  items-stretch">
                                @for (item of tableData.xData; track item) {
                                <div class="col style-border-right">
                                    <p class="row-title">{{item}}</p>
                                </div>
                                }
                            </div>
                        </div>
                    </div>
                    <!-- 一列 END-->
                    <!-- 一列 START-->
                    @for (contentDataItem of tableData.contentData; track contentDataItem; let index = $index) {
                    <div class="tr style-full-table">
                        <div class="td td-content p-0">
                            <div class="row  items-stretch">
                                <div class="col style-border-right">
                                    <p class="col-text">{{tableData.yData[index]}}</p>
                                </div>
                                @for (item of contentDataItem; track item; let index = $index) {
                                <div class="col style-border-right">
                                    <p class="col-text">{{item}}</p>
                                </div>
                                }
                            </div>
                        </div>
                    </div>
                    }
                    <!-- 一列 END-->
                </div>
            </div>
        </div>
    </div>
</div>