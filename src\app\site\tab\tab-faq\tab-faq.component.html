<div class="pages-content">
    <!--內容放置區-->
    <div class="faq-card-layout">
        <!--查詢條件-->
        <div class="form-module-layout" [formGroup]="form">
            <!-- <div class="form-module-item">
                        <p class="form-module-title">問題分類</p>
                        <div>
                            <span class="select-control w-full">
                                <select formControlName="type">
                                    @for (item of typeList; track item) {
                                    <option [value]="item.typeValue">{{item.typeName}}</option>
                                    }
                                </select>
                            </span>
                        </div>
                    </div> -->
            <div class="form-module-item">
                <p class="form-module-title"></p>
                <!-- 搜尋元件元件 START-->
                <div class="module-input">
                    <input type="search" class="form-control w-full" formControlName="keyWord" placeholder="請輸入關鍵字"
                        title="請輸入關鍵字">
                    <input class="btn-list btn-primary-color" value="搜尋" type="button" (click)="search()">
                </div>
                <!-- 搜尋元件元件 END-->
            </div>
        </div>
        @for (item of faqList; track item) {
        <div class="faq-card-item" [ngClass]="{'active': selectedFaqId === item.faqId}">
            <div class="faq-card-item-title" role="button" tabindex="0" (click)="toggleFaq(item.faqId)"
                (keydown.enter)="toggleFaq(item.faqId)" (keydown.space)="toggleFaq(item.faqId)"
                [attr.aria-expanded]="selectedFaqId === item.faqId" [attr.aria-controls]="item.faqId">
                <h3 class="text-lg font-medium text-foreground pr-4">{{item.question}}</h3>
                <div class="flex-shrink-0">
                    <span class="material-symbols-outlined">keyboard_arrow_down</span>
                </div>
            </div>
            <div class="faq-card-item-cont" [id]="item.faqId" role="region"
                [attr.aria-hidden]="selectedFaqId !== item.faqId"
                [style.maxHeight]="selectedFaqId === item.faqId ? '1000px' : '0'"
                [style.opacity]="selectedFaqId === item.faqId ? '1' : '0'">
                <div class="faq-card-item-cont-text">
                    <app-template [contentData]="item.answer"></app-template>
                </div>
            </div>
        </div>
        }@empty {
        <div class="not-found">
            <span>查無資料</span>
        </div>
        }
    </div>
    @if(faqList.length > 0){
    <app-paginator [pageSize]="pageSize" [nowPage]="nowPage" [totalRecords]="totalCount" [pageShowCount]="pageShowCount"
        currentPageReportTemplate="第 {first} 到 {last} 筆，共 {totalRecords} 筆"
        (clickPageEvent)="getPageFromPaginator($event)"
        (pageSizeChangeEvent)="getPageSizeFromPaginator($event)"></app-paginator>
    }
</div>