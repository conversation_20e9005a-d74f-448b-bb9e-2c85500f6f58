export interface guideLineReq {
  menuitemId: string;
  currentPage: string;
  pageSize: number;
  type: string;
}

export interface guidlineDataModel {
  title: string;
  totalCount: number;
  totalPage: number;
  currentPage: number;
  data: guideLineDataItem[];
}

export interface guideLineDataItem {
  sportType: string;
  name: string;
  fileId: string;
  fileName: string;
  fileUrl: string;
  fileType: string;
}
