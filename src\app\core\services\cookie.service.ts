import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class CookieService {
  constructor(private http: HttpClient) {}

  setCookie(): Observable<string> {
    return this.http.get<string>(`api/UserData/set-cookie?allow=true`);
  }
  getCookie(): Observable<string> {
    return this.http.get<string>(`api/UserData/get-cookie`);
  }
}
