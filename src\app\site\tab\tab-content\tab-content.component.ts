import { Component, Input } from '@angular/core';
import { getContentDataResp } from '../../../core/interface/content.interface';
import { dataItem } from '../../../core/interface/type.interface';
import { ContentService } from '../../../core/services/content.service';

@Component({
  selector: 'app-tab-content',
  standalone: false,

  templateUrl: './tab-content.component.html',
  styleUrl: './tab-content.component.scss',
})
export class TabContentComponent {
  @Input() menuItemId: string = '';
  contentData: dataItem[] = [];
  constructor(private contentService: ContentService) {}

  ngOnInit() {
    this.getContent();
  }

  getContent() {
    this.contentService.getContentData(this.menuItemId).subscribe({
      next: (resp: getContentDataResp) => {
        this.contentData = resp.data.new_NewsTagDatas;
      },
      error: () => {},
    });
  }
}
