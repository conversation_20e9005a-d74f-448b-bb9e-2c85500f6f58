import { CommonModule } from '@angular/common';
import {
  HttpClient,
  HTTP_INTERCEPTORS,
  withInterceptorsFromDi,
  provideHttpClient,
} from '@angular/common/http';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { BrowserModule } from '@angular/platform-browser';
import { TranslateModule, TranslateLoader } from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { HttpInterceptorService } from './core/services/interceptor/http-interceptor.service';
import { ErrorPageComponent } from './shared/components/error-page/error-page.component';
import { FooterComponent } from './shared/components/footer/footer.component';
import { HeaderComponent } from './shared/components/header/header.component';
import { LoadingPageComponent } from './shared/components/loading-page/loading-page.component';

import { InitJsDirective } from './shared/directives/init-js.directive';
import { SanitizePipe } from './shared/pipes/sanitize.pipe';
import { TypeNamePipe } from './shared/pipes/type-name.pipe';
import { YoutubeEmbedPipe } from './shared/pipes/youtube-embed.pipe';
import { YoutubeIdExtracterPipe } from './shared/pipes/youtube-id-extracter.pipe';
import { HomeComponent } from './site/home/<USER>';
import { IndexComponent } from './site/index/index.component';
import { NewsListComponent } from './site/news/news-list/news-list.component';
import { NewsComponent } from './site/news/news/news.component';
import { ImageListComponent } from './site/image/image-list/image-list.component';
import { ImageComponent } from './site/image/image/image.component';
import { LineListComponent } from './site/line/line-list/line-list.component';
import { LineComponent } from './site/line/line/line.component';
import { HtmlComponent } from './site/html/html.component';
import { HtmlZipComponent } from './site/html-zip/html-zip.component';
import { ContentComponent } from './site/content/content.component';
import { TabComponent } from './site/tab/tab.component';
import { ShareResourceComponent } from './site/share-resource/share-resource.component';
import { SurveyComponent } from './site/survey/survey.component';
import { VideoListComponent } from './site/video/video-list/video-list.component';
import { VideoComponent } from './site/video/video/video.component';
import { FaqComponent } from './site/faq/faq.component';
import { PaginatorComponent } from './shared/components/paginator/paginator.component';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { TemplateComponent } from './shared/template/template.component';
import { ParagraphTitleComponent } from './shared/template/paragraph-title/paragraph-title.component';
import { FileDataComponent } from './shared/template/file-data/file-data.component';
import { TitleComponent } from './shared/template/title/title.component';
import { PhotoComponent } from './shared/template/photo/photo.component';
import { PhotoListComponent } from './shared/template/photo-list/photo-list.component';
import { AddressComponent } from './shared/template/address/address.component';
import { ButtonComponent } from './shared/template/button/button.component';
import { CardComponent } from './shared/template/card/card.component';
import { TableComponent } from './shared/template/table/table.component';
import { VideoComponent as TemplateVideoComponent } from './shared/template/video/video.component';
import { ContentComponent as TemplateContentComponent } from './shared/template/content/content.component';
import { HtmlComponent as TemplateHtmlComponent } from './shared/template/html/html.component';
import { TabFaqComponent } from './site/tab/tab-faq/tab-faq.component';
import { TabHtmlComponent } from './site/tab/tab-html/tab-html.component';
import { TabHtmlZipComponent } from './site/tab/tab-html-zip/tab-html-zip.component';
import { TabContentComponent } from './site/tab/tab-content/tab-content.component';
import { TabShareResourceComponent } from './site/tab/tab-share-resource/tab-share-resource.component';
import { TabVideoComponent } from './site/tab/tab-video/tab-video.component';
import { TabSurveyComponent } from './site/tab/tab-survey/tab-survey.component';
import { ENewsletterListComponent } from './site/eNewsletter/e-newsletter-list/e-newsletter-list.component';
import { ENewsletterComponent } from './site/eNewsletter/e-newsletter/e-newsletter.component';
import { EbookListComponent } from './site/ebook/ebook-list/ebook-list.component';
import { EbookDetailComponent } from './site/ebook/ebook-detail/ebook-detail.component';
import { SpinnerComponent } from './spinner/spinner.component';
import { MatProgressSpinner } from '@angular/material/progress-spinner';

export function HttpLoaderFactory(http: HttpClient) {
  return new TranslateHttpLoader(http, './assets/i18n/', '.json');
}

@NgModule({
  declarations: [
    AppComponent,
    HeaderComponent,
    FooterComponent,
    HomeComponent,
    ErrorPageComponent,
    TypeNamePipe,
    SanitizePipe,
    InitJsDirective,
    YoutubeEmbedPipe,
    PaginatorComponent,
    YoutubeIdExtracterPipe,
    LoadingPageComponent,
    IndexComponent,
    NewsListComponent,
    NewsComponent,
    ImageListComponent,
    ImageComponent,
    LineListComponent,
    LineComponent,
    HtmlComponent,
    HtmlZipComponent,
    ContentComponent,
    TabComponent,
    ShareResourceComponent,
    SurveyComponent,
    VideoListComponent,
    VideoComponent,
    FaqComponent,
    TemplateComponent,
    SpinnerComponent,
    ParagraphTitleComponent,
    FileDataComponent,
    TitleComponent,
    PhotoComponent,
    PhotoListComponent,
    AddressComponent,
    ButtonComponent,
    CardComponent,
    TableComponent,
    TemplateVideoComponent,
    TemplateContentComponent,
    TemplateHtmlComponent,
    TabFaqComponent,
    TabHtmlComponent,
    TabHtmlZipComponent,
    TabContentComponent,
    TabShareResourceComponent,
    TabVideoComponent,
    TabSurveyComponent,
    ENewsletterListComponent,
    ENewsletterComponent,
    EbookListComponent,
    EbookDetailComponent,
  ],
  imports: [
    MatProgressSpinner,
    BrowserModule,
    AppRoutingModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpClient],
      },
    }),
    // TranslateModule.forRoot({
    //   loader: {
    //     provide: TranslateLoader,
    //     useFactory: (http: HttpClient) =>
    //       new TranslateHttpLoader(
    //         http,
    //         '/assets/i18n/',
    //         `.json?v=${new Date().getTime()}`
    //       ),
    //     deps: [HttpClient],
    //   },
    // }),
  ],
  providers: [
    provideHttpClient(withInterceptorsFromDi()), // 启用 DI 拦截器
    {
      provide: HTTP_INTERCEPTORS,
      useClass: HttpInterceptorService,
      multi: true,
    },
    provideAnimationsAsync(),
  ],
  bootstrap: [AppComponent],
})
export class AppModule {}
