// Scss Document

.table-list{
	padding: 0 0 20px;
}
// 變數定義區
$color-black: #177691;
$color-white: #fff;
$color-border: #CEF1F4;
$color-primary-900: #177691;
$color-primary-700: #177691;

// 共用樣式
@mixin cell-border {
  border-right: 1px solid $color-border;
  border-bottom: 1px solid $color-border;
}
@mixin td-sticky-style($bg) {
  background: $bg;
  color: $color-white;
}

// 主樣式
.item-table {
	border-radius: 22px;
	.tr {
		display: flex;
		align-items: stretch;

		&.style-full-table {
		  width: 800px;
		  @media (min-width: 768px) {
			width: auto;
		  }
		}
	}

  .td {
    @include cell-border;

    &.p-0 { padding: 0; }

    &:last-child { border-right: 0; }
  }

	.td-content {
		flex: 1 1 0;
		max-width: 100%;
		.row{
			display: flex;
			flex-wrap: wrap;
			align-items: stretch;
			height: 100%;
			&-title{
				font-size: 1em;
				font-weight: bold;
				text-align: center;
				width: 100%;
			}
		}
		.col{
			padding: 10px;
			display: flex;
			align-items: center;
			flex-basis: 0;
			flex-grow: 1;
			max-width: 100%;
			height: 100%;
			box-sizing: border-box;
			&-text{
				font-size: 1em;
				font-weight: bold;
			}
			
		}
	}

  .td-sticky,
  .td-sticky-2 {
    @include td-sticky-style($color-black);
	padding: 12px;
	  box-sizing: border-box;
    flex: 0 0 auto;
    width: auto !important;
    max-width: 100%;
    position: sticky;
    left: 0;
    z-index: 2;

    &.bg-primary-900 { background: $color-primary-900; }
    &.bg-primary-700 { background: $color-primary-700; }
  }

  .thead {
    width: 100%;
    .td {
      background: $color-black;
      color: $color-white;

      &:first-child { border-top-left-radius: 22px; }
      &:last-child { border-top-right-radius: 22px; }
    }

    .style-bg-primary-900 {
      .td-sticky,
      .td-content {
        @include td-sticky-style($color-primary-900);
        text-align: center;
      }
    }
  }

  .tbody {
    .td-content {
      background: $color-white;
      color: $color-black;
    }

    .td-sticky {
		word-break: auto-phrase;
		overflow-wrap: anywhere;
		display: flex;
		align-items: center;
    }

    .style-bg-primary-900 {
      .td-sticky,
      .td-content {
        @include td-sticky-style($color-primary-900);
        text-align: center;
      }
    }

    .style-thead {
      .td-content {
        background: $color-black;
        color: $color-white;
        text-align: center;
      }
      .td:first-child { border-top-left-radius: 12px; }
      .td:last-child { border-top-right-radius: 12px; }
    }

    .tr:last-child {
      border-bottom-left-radius: 22px;
      border-bottom-right-radius: 22px;

      .td {
        border-bottom: 0;
        &:first-child { border-bottom-left-radius: 12px; }
        &:last-child { border-bottom-right-radius: 12px; }
      }
    }
  }

  .box-sticky {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 4;
    height: 0;
    width: 100%;

    .td-content {
      background: $color-black;
      color: $color-white;
      text-align: center;
    }

    .td:first-child { border-top-left-radius: 12px; }
    .td:last-child { border-top-right-radius: 12px; }

    .td-stick-left {
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      z-index: 2;
    }
  }

  // 額外邊框樣式
  .style-border-right { border-right: 1px solid $color-border; }
  .style-border-right:last-child { border-right: 0; }

  .style-border-bottom { border-bottom: 1px solid $color-border; }
  .style-border-bottom:last-child { border-bottom: 0; }
}

// 欄寬控制
.td-width-normal {
  width: 100px;
  min-width: 100px;
  max-width: 100px !important;

  @media (min-width: 1400px) {
    width: 200px;
    min-width: 200px;
    max-width: 200px !important;
  }
}

.td-width-venue {
  width: 200px;
  min-width: 200px;
  max-width: 200px !important;

  @media (min-width: 1400px) {
    width: 410px;
    min-width: 410px;
    max-width: 410px !important;
  }
}

.td-width-para-discipline {
  width: 200px;
  min-width: 200px;
  max-width: 200px !important;

  @media (min-width: 1400px) {
    width: 410px;
    min-width: 410px;
    max-width: 410px !important;
  }
}

.td-width-sport-discipline {
  width: 100px;
  min-width: 100px;
  max-width: 100px !important;

  @media (min-width: 1400px) {
    width: 160px;
    min-width: 160px;
    max-width: 160px !important;
  }
}

// 英文語系專用寬度
html[lang="en"] .td-width-sport-discipline {
  width: 150px;
  min-width: 150px;
  max-width: 150px !important;

  @media (min-width: 1400px) {
    width: 160px;
    min-width: 160px;
    max-width: 160px !important;
  }
}

//表格外框
.item-box-table {
	border-radius: 22px;
	border: 1px solid #b3afbf;
	overflow: hidden;
	position: relative;
	::-webkit-scrollbar {
		width: 4px;
		height: 4px;
		border-radius: 4px;
	}
	::-webkit-scrollbar-track {
		background: #b3afbf;
		border-radius: 4px;
	}
	::-webkit-scrollbar-thumb {
		background: rgba(0, 0, 0, 0.3);
		border-radius: 4px;
	}
	:hover::-webkit-scrollbar-thumb, &:focus ::-webkit-scrollbar-thumb {
		background: #000;
	}
	&.rollin.is-in .item-grab-tutorial {
		opacity: 0;
	}
	  &:hover .item-grab-tutorial, &:focus .item-grab-tutorial {
		opacity: 0;
		transition: opacity 0.6s 0.2s;
	  }
	//卷軸說明
	.item-grab-tutorial {
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 5;
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.5);
		pointer-events: none;
		transition: opacity 0.6s 2s;
		border-radius: 22px;
		color: #fff;
		span{
			display: flex;
			justify-content: center;
			align-items: center;
		}
		&-text{
			
			font-weight: bold;
		}
	}
}

.item-box-table-tbody {
	width: 100%;
	border-bottom-left-radius: 22px;
	border-bottom-right-radius: 22px;
	overflow: auto;
	-webkit-overflow-scrolling: touch;

	.tbody {
		pointer-events: none;
		-webkit-user-select: none;
		-moz-user-select: none;
		user-select: none;
		display: table;
		min-width: 100%;
		width: max-content;
	}
}

