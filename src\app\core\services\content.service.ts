import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ShareService } from './share.service';
import { getContentDataResp } from '../interface/content.interface';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class ContentService {
  constructor(private http: HttpClient, private shareService: ShareService) {}

  /**
   * 取得內容頁
   * @param menuitemId
   * @returns
   */
  getContentData(menuitemId: string): Observable<getContentDataResp> {
    return this.http.get<getContentDataResp>(
      'api/New_News/GetNew_News_Content',
      {
        params: {
          new_menuitemId: menuitemId,
          lang: this.shareService.getLang(),
        },
      }
    );
  }
  /**
   * 取得內容頁預覽用
   * @param menuitemId
   */
  getContentDataForView(menuitemId: string): Observable<getContentDataResp> {
    return this.http.get<getContentDataResp>(
      'api/New_News/GetNew_News_ContentForView',
      {
        params: {
          menuitemId: menuitemId,
          lang: this.shareService.getLang(),
        },
      }
    );
  }
}
