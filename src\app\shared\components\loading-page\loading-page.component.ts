import { Component } from '@angular/core';
import { LoadingService } from '../../utilities/loading.service';
import { Observable } from 'rxjs';

declare var loadingAnimate: any;
@Component({
  selector: 'app-loading-page',
  standalone: false,

  templateUrl: './loading-page.component.html',
  styleUrl: './loading-page.component.scss'
})

export class LoadingPageComponent {
  loading$!: Observable<boolean>;

  constructor(
    private loadingServ: LoadingService
  ) {

  }

  ngOnInit(): void {
    this.loading$ = this.loadingServ.loading$;

  }

  ngAfterViewInit(): void {
    loadingAnimate();
  }
}
