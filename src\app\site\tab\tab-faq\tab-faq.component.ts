import { Component, Input } from '@angular/core';
import { FormGroup, FormBuilder } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import {
  faqItem,
  getFaqListResp,
  getFaqListReq,
} from '../../../core/interface/faq.interface';
import { FaqService } from '../../../core/services/FaqService';
import { ShareService } from '../../../core/services/share.service';

@Component({
  selector: 'app-tab-faq',
  standalone: false,

  templateUrl: './tab-faq.component.html',
  styleUrl: './tab-faq.component.scss',
})
export class TabFaqComponent {
  @Input() menuItemId: string = '';
  form: FormGroup;
  isPreview: boolean = false;
  title: string = '';
  selectedFaqId: string = '';

  keyWord: string = '';
  typeGroupId: string = '';
  faqList: faqItem[] = [];
  nowPage: number = 1;
  pageSize: number = 10;
  pageShowCount: number = 5; //分頁器秀幾個
  totalPage: number = 0;
  totalCount: number = 0;

  constructor(
    private faqService: FaqService,
    private activatedRoute: ActivatedRoute,
    private shareService: ShareService,
    private fb: FormBuilder
  ) {

    this.form = this.fb.group({
      keyWord: [''],
    });
  }

  ngOnInit() {
    this.getFaqList();
  }

  search() {
    this.nowPage = 1;
    this.getFaqList();
  }

  getFaqList() {
    let req: getFaqListReq = {
      menuitemId: this.menuItemId,
      currentPage: this.nowPage,
      pageSize: this.pageSize,
      type: '',
      keyword: this.form.value.keyWord,
      lang: this.shareService.getLang(),
    };
    this.faqService.getFaqList(req).subscribe({
      next: (resp: getFaqListResp) => {
        this.title = resp.data.title;
        this.totalCount = resp.data.totalCount;
        this.totalPage = resp.data.totalPage;
        this.faqList = resp.data.data;
        if (this.faqList.length > 0) {
          this.selectedFaqId = this.faqList[0].faqId;
        }
      },
      error: () => {},
    });
  }

  toggleFaq(faqId: string) {
    this.selectedFaqId = this.selectedFaqId === faqId ? '' : faqId;
  }

  /**
   * 第幾頁
   * @param item number
   */
  getPageFromPaginator(nowPage: number) {
    this.nowPage = nowPage;
    this.getFaqList();
  }
  /**
   * 每頁資料數量
   * @param item number
   */
  getPageSizeFromPaginator(pageSize: number) {
    this.pageSize = pageSize;
    this.nowPage = 1;
    this.getFaqList();
  }
}
