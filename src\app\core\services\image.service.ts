import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ShareService } from './share.service';
import {
  getImageListReq,
  getImageListResp,
} from '../interface/image.interface';
import { Observable } from 'rxjs';
import { getNewsResp } from '../interface/news.interface';

@Injectable({
  providedIn: 'root',
})
export class ImageService {
  constructor(private http: HttpClient, private shareService: ShareService) {}

  getImageList(req: getImageListReq): Observable<getImageListResp> {
    return this.http.post<any>('api/New_News/GetNew_ImageDataList', req);
  }

  getImage(typeGroupId: string): Observable<getNewsResp> {
    return this.http.get<getNewsResp>('api/New_News/GetNew_NewsData', {
      params: {
        typeGroupId: typeGroupId,
        lang: this.shareService.getLang(),
      },
    });
  }

  getImageForView(typeGroupId: string): Observable<getNewsResp> {
    return this.http.get<getNewsResp>('api/New_News/GetNew_NewsDataForView', {
      params: {
        typeGroupId: typeGroupId,
        lang: this.shareService.getLang(),
      },
    });
  }
}
