import {
  AfterViewInit,
  Component,
  ElementRef,
  Input,
  OnInit,
  ViewChild,
} from '@angular/core';
import { TableDataList } from '../../../core/interface/type.interface';

@Component({
  selector: 'app-table',
  standalone: false,

  templateUrl: './table.component.html',
  styleUrl: './table.component.scss',
})
export class TableComponent implements OnInit, AfterViewInit {
  @Input() tableData: TableDataList = { contentData: [], xData: [], yData: [] };
  @ViewChild('scrollContainer') scrollContainer!: ElementRef;
  isDown = false;
  startX = 0;
  scrollLeft = 0;

  constructor() {}
  ngOnInit() {
    console.log(this.tableData);
  }
  ngAfterViewInit() {
    const container = this.scrollContainer.nativeElement;

    container.addEventListener('mousedown', (e: MouseEvent) => {
      this.isDown = true;
      container.classList.add('is-grabbing');
      this.startX = e.pageX - container.offsetLeft;
      this.scrollLeft = container.scrollLeft;
    });

    container.addEventListener('mouseleave', () => {
      this.isDown = false;
      container.classList.remove('is-grabbing');
    });

    container.addEventListener('mouseup', () => {
      this.isDown = false;
      container.classList.remove('is-grabbing');
    });

    container.addEventListener('mousemove', (e: MouseEvent) => {
      if (!this.isDown) return;
      e.preventDefault();
      const x = e.pageX - container.offsetLeft;
      const walk = (x - this.startX) * 1.5;
      container.scrollLeft = this.scrollLeft - walk;
    });
  }
}
