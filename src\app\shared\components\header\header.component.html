<header class="header-layout">
  <div class="header-top">
    <div class="header-top-logo">
      <h1><a href="#" title="回首頁"><img src="assets/images/header/m-logo.svg" alt="財團法人原住民族語言研究發展基金會"></a></h1>
    </div>
    <div class="header-top-list">
      <div class="header-top-list-infor">
        <a href="#" class="header-top-list-btn">
          <span class="material-symbols-outlined">
            person
          </span>
          <span>會員專區</span>
        </a>
        <a href="" class="header-top-list-btn" (click)="changeLang();$event.preventDefault()">
          <span class="material-symbols-outlined">
            language
          </span>
          <span>中文</span>
        </a>
        <div class="fontChange">
          <p>字級大小：</p>
          <ul>
            <li class="font_l" [class.active]="fontSize === FontSize.big"><a href=""
              (click)="changeFontsize($event,FontSize.big)">A</a></li>
            <li class="font_m active" [class.active]="fontSize === FontSize.middle"><a href=""
                (click)="changeFontsize($event,FontSize.middle)">A</a></li>
            <li class="font_s" [class.active]="fontSize === FontSize.small"><a href=""
              (click)="changeFontsize($event,FontSize.small)">A</a></li>
          </ul>
        </div>
      </div>
      <!-- 漢堡選單按鈕 -->
      <div class="header-hamburger-btn" [ngClass]="{ 'open': isPhoneMenuOpenStatus }" title="手機選單">
        @if(isPhoneMenuOpenStatus){
        <div class="header-hamburger-btn-opened-menu">
          <span class="material-symbols-outlined" (click)="isPhoneMenuOpenStatus = !isPhoneMenuOpenStatus">close</span>
        </div>
        }@else{
        <div class="header-hamburger-btn-closed-menu">
          <span class="material-symbols-outlined" (click)="isPhoneMenuOpenStatus = !isPhoneMenuOpenStatus">dehaze</span>
        </div>
        }
      </div>
    </div>

  </div>
  <!--桌機選單-->
  <nav class="nav-list-layout">
    <div class="nav-list">
      <ul class="nav-list-cont">
        @for (item of menuList; track item) {
        <li class="nav-list-item" [ngClass]="{ 'active': item.id === menuId }">
          <button class="nav-list-item-btn" data-target="about" [title]="item.name" (click)="clickMenu(item)">
            {{lang ==='zh' ? item.name : item.enName}}</button>
        </li>
        }
      </ul>
    </div>
    <div class="nav-second-cont" [ngClass]="{ 'active': menuId }">
      <div class="nav-second-cont-list">
        @for (subItem of menuSubList; track subItem) {
        <div class="nav-second-cont-list-item">
          <!-- 次選單第二層-標題-->
          <div class="nav-second-cont-list-sublink">
            @if(subItem.inverseParent.length < 1) { <a href="" (click)="goPage(subItem);$event.preventDefault()"
              [title]="lang === 'zh' ? subItem.name : subItem.enName">
              <span>{{lang === 'zh' ? subItem.name : subItem.enName}}</span>
              <span class="material-symbols-outlined">
                keyboard_arrow_right
              </span>
              </a>
              }@else{
              <span>{{lang === 'zh' ? subItem.name : subItem.enName}}</span>
              }
          </div>
          @for (inverseItem of subItem.inverseParent; track inverseItem) {
          <!-- 次選單第三層 start-->
          <div class="nav-third-cont">
            <!-- 一個次選單第三層連結 start-->
            <div class="nav-second-cont-list-sublink">
              <a href="" (click)="goPage(inverseItem);$event.preventDefault()"
                [title]="lang === 'zh' ? inverseItem.name : inverseItem.enName">
                <span>{{lang === 'zh' ? inverseItem.name : inverseItem.enName}}</span>
                <span class="material-symbols-outlined">
                  keyboard_arrow_right
                </span>
              </a>
            </div>
          </div>
          }
        </div>
        }
      </div>
    </div>
  </nav>
</header>

<!-- 手機漢堡選單 start-->
<div class="hamburger-content" [ngClass]="{ 'active': isPhoneMenuOpenStatus }">
  <!-- 手機選單內容 start-->
  <nav class="hamburger-content-nav">
    @for(item of menuList; track item){
    @if(item.type !== menuType.Folder){
    <a class="hamburger-content-nav-link-button" href="" (click)="goPage(item);$event.preventDefault()" title="前往新聞稿">
      <span>{{lang === 'zh' ? item.name : item.enName}}</span>
      <span class="material-symbols-outlined">
        keyboard_arrow_right
      </span>
    </a>
    }@else{
    <!-- 一個大單元 start -->
    <div class="hamburger-content-nav-group">
      <!-- 第一層 - 按鈕-->
      <div class="hamburger-content-nav-link">
        <button class="hamburger-content-nav-link-button" title="開啟關於本會選單" data-title-open="開啟關於本會選單"
          data-title-close="關閉關於本會選單" aria-expanded="false" aria-controls="mobile-menu-about"
          (click)="item.isMenuOpen = !item.isMenuOpen">
          <span>{{lang === 'zh' ? item.name : item.enName}}</span>
          <div class="hamburger-content-nav-link-icon">
            @if(item.isMenuOpen){
            <div class="on-open">
              <span class="material-symbols-outlined">
                remove
              </span>
            </div>
            }@else{
            <div class="on-close">
              <span class="material-symbols-outlined">
                add
              </span>
            </div>
            }
          </div>
        </button>
      </div>
      <!-- 第一層 - 展開內容-->

      <div class="hamburger-nav-content" [ngClass]="{'close': !item.isMenuOpen ,'open': item.isMenuOpen}">
        <!-- 第一層 - 展開內容 - 目錄部分-->
        <div class="hamburger-content-nav-subnav">
          @for(subItem of item.inverseParent; track subItem){
          @if(subItem.type!==menuType.Folder){
          <!-- 一個第二層目錄 start-->
          <div class="hamburger-content-nav-item">
            <!-- 第二層 - 按鈕-->
            <div class="hamburger-content-nav-link">
              <a class="hamburger-content-nav-link-button second-cont" href="#" target="_self" title="前往關於本會">
                <span>{{lang === 'zh' ? subItem.name : subItem.enName}}</span>
                <span class="material-symbols-outlined">
                  keyboard_arrow_right
                </span>
              </a>
            </div>
          </div>
          }@else{
          <!-- 一個第二層目錄 end-->
          <!-- 一個第二層目錄 start-->
          <div class="hamburger-content-nav-item">
            <!-- 第二層 - 按鈕-->
            <div class="hamburger-content-nav-link">
              <button class="hamburger-content-nav-link-button second-cont" title="開啟董監事選單" data-title-open="開啟董監事選單"
                data-title-close="關閉董監事選單" aria-expanded="false" aria-controls="mobile-menu-about"
                (click)="subItem.isMenuOpen = !subItem.isMenuOpen">
                <span>{{lang === 'zh' ? subItem.name : subItem.enName}}</span>
                <div class="hamburger-content-nav-link-icon">
                  @if(subItem.isMenuOpen){
                  <div class="on-open">
                    <span class="material-symbols-outlined">
                      remove
                    </span>
                  </div>
                  }@else{
                  <div class="on-close">
                    <span class="material-symbols-outlined">
                      add
                    </span>
                  </div>
                  }
                </div>
              </button>
            </div>
            <!-- 第二層 - 展開內容-->
            <div class="hamburger-content-nav" [ngClass]="{'close': !subItem.isMenuOpen ,'open': subItem.isMenuOpen}">
              @for(inverseItem of subItem.inverseParent; track inverseItem){
              <!-- 一個第三層目錄 start-->
              <div class="hamburger-content-nav-item">
                <!-- 第三層 - 按鈕-->
                <div class="hamburger-content-nav-link">
                  <a class="hamburger-content-nav-link-button third-cont" href="#" target="_self" title="前往董事會">
                    <span>{{lang === 'zh' ? inverseItem.name : inverseItem.enName}}</span>
                    <span class="material-symbols-outlined">
                      keyboard_arrow_right
                    </span>
                  </a>
                </div>
              </div>
              <!-- 一個第三層單元 end-->
              }
            </div>
          </div>
          <!-- 一個第二層目錄 end-->
          }
          }
        </div>
      </div>
    </div>
    <!-- 一個大單元 end-->
    }
    }
  </nav>
  <!-- 手機選單內容 end-->
</div>
<!-- 手機漢堡選單 end-->