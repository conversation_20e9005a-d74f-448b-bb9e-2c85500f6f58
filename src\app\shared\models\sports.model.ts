import { listDataModel } from "./news.model";

export interface sportDataModel extends listDataModel {
  data?: sportItem[];
}

export interface sportItem {
  titleName:string;
  sportDataId: string;
  title: string;
  coverUrl: string;
  createUser: string;
  editUser: string;
  sportType: string;
}

export interface sportPlaceDataModel extends listDataModel {
  data?: sportPlaceItem[];
}

export interface sportPlaceItem {
  sportPlaceDataId: string;
  title: string;
  coverUrl: string;
  createUser: string;
  editUser: string;
  sportType: string[];
}
