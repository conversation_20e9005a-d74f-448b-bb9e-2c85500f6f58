// Scss Document
.radio-list{
	padding: 0 15px 0 0;
	display: inline-flex;
    position: relative;
    flex-direction: row;
    align-items: center;
	gap: 0.2rem;
	label{
		display: inline-block;
		font-size: 1.125em;
		width: max-content;
		line-height: 1.7;
	}
	input[type="radio"]{
		margin: 0 15px 0 0;
		display: inline-block;
		position: relative;
		opacity: 0;
		&:checked{
			+ label:before{
				content: "";
				height: 1.25rem;
				width: 1.25rem;
				display: block;
				background-color: #fff !important;
				border: .3rem solid #177691;
				border-radius: 50%;
				position: absolute;	
				
			}
		}		
		+ label:before{
			content: "";
			height: 1.5rem;
			width: 1.5rem;
			display: block;
			border: .1rem solid #dbdde0;
			border-radius: 50%;
			position: absolute;
			background-color: #fff;
			left: 0;
			top: calc(50% - 0.9rem);
		}			
		&:disabled{
			+ label:before{
				height: 1.25rem;
				width: 1.25rem;
				background-color: #dbdde0;
				border: .3rem solid #dbdde0;
			}
		}
	}
		
}
	