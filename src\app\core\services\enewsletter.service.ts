import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ShareService } from './share.service';
import { Observable } from 'rxjs';
import {
  checkSubscribeResp,
  checkUnsubscribeResp,
  getENewsLetterDetailResp,
  getENewsLetterListResp,
  subscribeReq,
  unsubscribeReq,
} from '../interface/enewsletter.interface';
import { defaultItem } from '../interface/share.interface';

@Injectable({
  providedIn: 'root',
})
export class ENewsletterService {
  constructor(
    private httpClient: HttpClient,
    private shareService: ShareService
  ) {}

  /**
   * 取得電子報清單
   * @param nowPage
   * @param pageSize
   * @returns
   */
  getENewsLetterList(
    nowPage: number,
    pageSize: number
  ): Observable<getENewsLetterListResp> {
    return this.httpClient.get<getENewsLetterListResp>(
      'api/E_Newsletter/GetE_NewsletterList',
      {
        params: {
          currentPage: nowPage,
          pageSize: pageSize,
        },
      }
    );
  }

  getENewsLetterDetail(
    typeGroupId: string
  ): Observable<getENewsLetterDetailResp> {
    return this.httpClient.get<getENewsLetterDetailResp>(
      `api/E_Newsletter/PreviewFrontE_Newsletter`,
      {
        params: {
          typeGroupId: typeGroupId,
        },
      }
    );
  }

  getCaptcha(): Observable<Blob> {
    return this.httpClient.get(`api/Captcha/GetCaptcha`, {
      responseType: 'blob',
    });
  }

  subscribe(req: subscribeReq): Observable<defaultItem> {
    return this.httpClient.post<defaultItem>(
      'api/E_Newsletter/SendSubscribeEmail',
      req
    );
  }

  unsubscribe(req: unsubscribeReq): Observable<defaultItem> {
    return this.httpClient.post<defaultItem>(
      'api/E_Newsletter/SendUnSubscribeEmail',
      req
    );
  }

  checkSubscribe(token: string): Observable<checkSubscribeResp> {
    return this.httpClient.post<checkSubscribeResp>(
      'api/E_Newsletter/VerifySubscribe',
      {
        token: token,
      }
    );
  }

  checkUnsubscribe(token: string): Observable<checkUnsubscribeResp> {
    return this.httpClient.post<checkUnsubscribeResp>(
      'api/E_Newsletter/VerifyUnSubscribe',
      {
        token: token,
      }
    );
  }
}
