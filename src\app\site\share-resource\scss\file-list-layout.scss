// Scss Document
.file-list-layout{
	display: flex;
	flex-direction: column;
	.file-list-cont{
		padding: 20px 0 0;
		&-item{
			padding: 0 0 20px;
			font-size: 1.125em;
			&-list{
				padding: 15px 20px;
				display: flex;
				flex-direction: row;
				align-items: center;
				justify-content: space-between;
				gap: 1rem;
				border-bottom: 1px solid #65676B;
			}
			&-title{
				margin: 0;
				padding: 0;
				display: block;
				overflow:hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
				width: 100%;
				font-weight: bold;
				text-align-last: left;
			}
			&-icon{
				margin: 0;
				padding: 5px;
				display: inline-flex;
				align-items: center;
				justify-content: center;
				color: #fff;
				background-color: #177691;
				border-radius: 40px;
				text-decoration: none;
				font-weight: bold;
				font-size: 1em;
				text-align: right;
			}
		}
		
	}
}
