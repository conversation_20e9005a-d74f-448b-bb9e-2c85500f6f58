<main class="main-layout">
    <div class="pages-layout">
        <h2 class="pages-title">{{title}}</h2>
        <div class="pages-content">
            <div class="form-module-layout" [formGroup]="form">
                @for (item of surveyField; track item.fieldId) {
                @switch (item.fieldType) {
                @case (FieldType.Input) {
                <div class="form-module-item  form-module-item-full">
                    <p class="form-module-title">
                        @if(item.required){
                        <span class="form-module-title-imp"></span>
                        }
                        {{item.fieldName}}
                    </p>
                    <input type="text" class="form-control w-full" placeholder="請輸入" [formControlName]="item.fieldId">
                </div>
                }
                @case (FieldType.Address) {
                <div class="form-module-item  form-module-item-full">
                    <p class="form-module-title">
                        @if(item.required){
                        <span class="form-module-title-imp"></span>
                        }
                        {{item.fieldName}}
                    </p>
                    <input type="text" class="form-control w-full" placeholder="請輸入" [formControlName]="item.fieldId">
                </div>
                }
                @case (FieldType.IDnumber) {
                <div class="form-module-item  form-module-item-full">
                    <p class="form-module-title">
                        @if(item.required){
                        <span class="form-module-title-imp"></span>
                        }
                        {{item.fieldName}}
                    </p>
                    <input type="text" class="form-control w-full" placeholder="請輸入" [formControlName]="item.fieldId">
                </div>
                }
                @case (FieldType.Phone) {
                <div class="form-module-item  form-module-item-full">
                    <p class="form-module-title">
                        @if(item.required){
                        <span class="form-module-title-imp"></span>
                        }
                        {{item.fieldName}}
                    </p>
                    <input type="text" class="form-control w-full" placeholder="請輸入" [formControlName]="item.fieldId">
                </div>
                }
                @case (FieldType.Mail) {
                <div class="form-module-item  form-module-item-full">
                    <p class="form-module-title">
                        @if(item.required){
                        <span class="form-module-title-imp"></span>
                        }
                        {{item.fieldName}}
                    </p>
                    <input type="text" class="form-control w-full" placeholder="請輸入" [formControlName]="item.fieldId">
                </div>
                }
                @case (FieldType.Select) {
                <div class="form-module-item  form-module-item-full">
                    <p class="form-module-title">
                        @if(item.required){
                        <span class="form-module-title-imp"></span>
                        }
                        {{item.fieldName}}
                    </p>
                    <span class="select-control w-full">
                        <select [formControlName]="item.fieldId">
                            <option [ngValue]="null" disabled>請選擇</option>
                            @for (optionItem of item.optionItems; track optionItem.value) {
                            <option [value]="optionItem.value">{{optionItem.value}}</option>
                            }
                        </select>
                    </span>
                </div>
                }
                @case (FieldType.TextArea) {
                <div class="form-module-item  form-module-item-full">
                    <p class="form-module-title">
                        @if(item.required){
                        <span class="form-module-title-imp"></span>
                        }
                        {{item.fieldName}}
                    </p>
                    <textarea class="form-control w-full" placeholder="請輸入" [formControlName]="item.fieldId"></textarea>
                </div>
                }
                @case (FieldType.Date) {
                <div class="form-module-item  form-module-item-full">
                    <p class="form-module-title">
                        @if(item.required){
                        <span class="form-module-title-imp"></span>
                        }
                        {{item.fieldName}}
                    </p>
                    <input type="date" class="form-control w-full" [formControlName]="item.fieldId">
                </div>
                }
                @case (FieldType.Radio) {
                <div class="form-module-item  form-module-item-full">
                    <p class="form-module-title">
                        @if(item.required){
                        <span class="form-module-title-imp"></span>
                        }
                        {{item.fieldName}}
                    </p>
                    <div class="form-module-item-list">
                        @for (optionItem of item.optionItems; let j = $index; track optionItem.value) {
                        <span class="radio-list">
                            <input [id]="item.fieldId + '_' + j" type="radio" [formControlName]="item.fieldId"
                                [value]="optionItem.value" />
                            <label [for]="item.fieldId + '_' + j">{{optionItem.value}}</label>
                        </span>
                        }
                    </div>
                </div>
                }
                @case (FieldType.CheckBox) {
                <div class="form-module-item  form-module-item-full" [formGroupName]="item.fieldId">
                    <p class="form-module-title">
                        @if(item.required){
                        <span class="form-module-title-imp"></span>
                        }
                        {{item.fieldName}}
                    </p>
                    <div class="form-module-item-list">
                        @for (optionItem of item.optionItems; track optionItem.value) {
                        <span class="checkbox-list">
                            <input [id]="item.fieldId + '_' + optionItem.value" type="checkbox"
                                [formControlName]="optionItem.value" />
                            <label [for]="item.fieldId + '_' + optionItem.value">{{optionItem.value}}</label>
                        </span>
                        }
                    </div>
                </div>
                }
                @case (FieldType.ImageCheckBox) {
                <div class="form-module-item  form-module-item-full" [formGroupName]="item.fieldId">
                    <p class="form-module-title">
                        @if(item.required){
                        <span class="form-module-title-imp"></span>
                        }
                        {{item.fieldName}}
                    </p>
                    <div class="form-module-item-list">
                        @for (metaItem of item.metaItems; track metaItem.value) {
                        <div class="form-img-layout">
                            <span class="form-img-cont">
                                <img [src]="metaItem.src" [alt]="metaItem.value">
                            </span>
                            <span class="checkbox-list">
                                <input [id]="item.fieldId + '_' + metaItem.value" type="checkbox"
                                    [formControlName]="metaItem.value" />
                                <label [for]="item.fieldId + '_' + metaItem.value">{{metaItem.value}}</label>
                            </span>
                        </div>
                        }
                    </div>
                </div>
                }
                }
                }
            </div>
            <div>
                <input class="btn-list btn-default-color" value="取消" type="button">
                <button class="btn-list btn-primary-color" type="button" (click)="submit()"
                    [disabled]="form.invalid">確定</button>
            </div>
        </div>
    </div>
</main>