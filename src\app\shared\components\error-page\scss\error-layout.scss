// Scss Document
.error{
	&-layout{
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		height: 100%;
	}
	&-item{
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		gap: 1.5rem;
	}
	&-img{
		display: inline-block;
		img{
			width: 100%;
		}
	}
	&-cont{
		p{
			margin: 0;
			padding: 0 0 10px;
			line-height: 1.6;
		}
		&-title{
			color: #214E57;
			font-size: 6em;
			font-weight: bold;
		}
		&-sub-title{
			font-size: 2.125em;
			font-weight: bold;
		}
		&-text{
			font-size: 1.25em;
		}
		&-link{
				padding: 12px 30px;
				overflow: hidden;
				display: inline-flex;
				align-items: center;
				justify-content: center;
				color: #fff;
				background-color: #177691;
				border-radius: 40px;
				text-decoration: none;
				font-weight: bold;
				font-size: 1em;
			}
	}
}

.main-layout {
	min-height: calc(100% - 245px);
    justify-content: center;
}
@media (max-width: 768px) {
	.error{
		&-layout{
			padding: 30px 0;
		}
		&-item{
			flex-direction: column;
		}
		&-cont{
			text-align: center;
	}
	}
}