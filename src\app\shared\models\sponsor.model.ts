export interface SponsorItem {
  coverDataId: string;
  linkUrl: string;
  type: string;
  sponsorId: string;
  coverUrl: string;
  name: string;
  menuitemId: string;
}

export interface SponsorData {
  topOfficialSponsorPartner: SponsorItem[];
  officialSponsorPartner: SponsorItem[];
  officialSponsor: SponsorItem[];
  officialSupplier: SponsorItem[];
  officialPlace: SponsorItem[];
  partner: SponsorItem[];
}
