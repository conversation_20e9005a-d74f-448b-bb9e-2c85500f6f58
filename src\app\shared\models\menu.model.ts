export interface MenuItem {
  id?: string;
  parentId?: string;
  webSiteId?: string;
  name?: string;
  type?: string;
  meta?: string;
  sort?: number;
  visibility?: string;
  onSiteMap?: boolean;
  moreItemText?: string;
  iconDataId?: string;
  linkMenuItemId?: string;
  hiddenTitle?: boolean;
  hiddenPageTitle?: boolean;
  bannerLdataId?: string;
  bannerMdataId?: string;
  bannerSdataId?: string;
  useChildrenBanner?: boolean;
  path?: string[];
  iconUrl?: string;
  bannerLUrl?: string;
  bannerMUrl?: string;
  bannerSUrl?: string;
  editable?: boolean;
  inverseParent: any[];
}

export interface getMenuNameResp {
  code: number;
  message: string;
  data: menuNameItem;
}

export interface menuNameItem {
  menuitemParent: string;
  menuitemChild: string;
}
