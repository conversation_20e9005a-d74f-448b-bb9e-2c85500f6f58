<main class="main-layout">
    <div class="pages-layout">
        <h2 class="pages-title">{{title}}</h2>
        <!--內容放置區-->
        <div class="pages-content">
            <div class="pages-content-lsit">
                <!--列表-->
                @for ( item of shareResourceList; track item) {
                <div class="file-list-layout">
                    <div class="file-list-cont">
                        <article class="file-list-cont-item" [title]="item.name" (click)="openFile(item.fileUrl)">
                            <div class="file-list-cont-item-list">
                                <h3 class="file-list-cont-item-title">{{item.name}}</h3>
                                <p class="file-list-cont-item-icon">
                                    <span class="material-symbols-outlined">
                                        download
                                    </span>
                                </p>
                            </div>
                        </article>
                    </div>
                </div>
                }
                @if(shareResourceList.length > 0){
                <app-paginator [pageSize]="pageSize" [nowPage]="nowPage" [totalRecords]="totalCount"
                    [pageShowCount]="pageShowCount" currentPageReportTemplate="第 {first} 到 {last} 筆，共 {totalRecords} 筆"
                    (clickPageEvent)="getPageFromPaginator($event)"
                    (pageSizeChangeEvent)="getPageSizeFromPaginator($event)"></app-paginator>
                }@else{
                <div class="not-found">
                    <span>查無資料</span>
                </div>
                }
            </div>
        </div>
    </div>
</main>