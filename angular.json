{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"lrdf_Forestage": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss", "standalone": false}, "@schematics/angular:directive": {"standalone": false}, "@schematics/angular:pipe": {"standalone": false}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/lrdf_Forestage", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["@angular/material/prebuilt-themes/azure-blue.css", "src/styles.scss"], "scripts": []}, "configurations": {"production": {"outputPath": "dist/lrdf_Forestage/prod", "budgets": [{"type": "initial", "maximumWarning": "100MB", "maximumError": "100MB"}, {"type": "anyComponentStyle", "maximumWarning": "100MB", "maximumError": "100MB"}], "aot": true, "outputHashing": "all", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.production.ts"}]}, "development": {"optimization": true, "outputPath": "dist/lrdf_Forestage/dev", "sourceMap": true, "aot": true, "outputHashing": "all", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev.ts"}]}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"proxyConfig": "src/proxy.conf.json"}, "configurations": {"production": {"buildTarget": "lrdf_Forestage:build:production"}, "development": {"buildTarget": "lrdf_Forestage:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["@angular/material/prebuilt-themes/azure-blue.css", "src/styles.scss"], "scripts": []}}}}}, "cli": {"analytics": false}}