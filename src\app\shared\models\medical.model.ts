export interface medicalServiceResp {
  code: number;
  message: string;
  data: medicalServiceModel;
}

export interface medicalServiceModel {
  title: string;
  data: medicalServiceItem[];
  count: number;
}

export interface medicalServiceItem {
  placeName: string;
  address: string;
  number: string;
  mapUrl: string;
  sportClass: string;
  sportPlace: string;
  sportType: string;
}

export interface getMedicalServiceSelectListResp {
  code: number;
  message: string;
  data: {
    conuntyList: string[];
    sportTypeList: string[];
    sportPlaceList: string[];
  };
}

export interface getMedicalServiceCountyListResp {
  code: number;
  message: string;
  data: string[];
}

export interface getMedicalServiceSportPlaceListResp {
  code: number;
  message: string;
  data: string[];
}

export interface getMedicalServiceListReq {
  menuitemId: string;
  sportPlace: string;
  county: string;
  city: string;
}

export interface getMedicalServiceListResp {
  code: number;
  message: string;
  data: {
    title: string;
    count: number;
    data: medicalServiceItem[];
  };
}

export interface medicalServiceItem {
  placeName: string;
  address: string;
  number: string;
  mapUrl: string;
  sportClass: string;
  sportPlace: string;
  sportType: string;
}
