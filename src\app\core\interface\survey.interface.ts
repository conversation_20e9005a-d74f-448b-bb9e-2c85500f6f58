import { defaultItem } from './share.interface';

export interface getSurveyResp extends defaultItem {
  data: {
    title: string;
    menuitemId: string;
    surveyId: string;
    levelDecision: number;
    currentLevel: number;
    surveyField: surveyField[];
    creater: string;
    editor: string;
    lang: string;
  };
}
export interface surveyField {
  fieldId: string;
  fieldName: string;
  fieldType: string;
  fieldMeta: string;
  sort: number;
  required: true;
  metaItems?: metaItem[];
  optionItems?: optionItem[];
}

export interface metaItem {
  src: string;
  value: string;
}

export interface optionItem {
  value: string;
}

export interface sendSurveyReq {
  surveyId: string;
  surveyResultData: surveyResultData[];
}

export interface surveyResultData {
  surveyFieldId: string;
  value: string;
}
