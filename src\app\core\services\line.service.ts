import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { getLineListReq, getLineListResp } from '../interface/line.interface';
import { Observable } from 'rxjs';
import { getNewsResp } from '../interface/news.interface';
import { ShareService } from './share.service';

@Injectable({
  providedIn: 'root',
})
export class LineService {
  constructor(private http: HttpClient, private shareService: ShareService) {}

  getLineList(req: getLineListReq): Observable<getLineListResp> {
    return this.http.post<getLineListResp>(
      'api/New_News/GetNew_LineDataList',
      req
    );
  }

  getLine(typeGroupId: string): Observable<getNewsResp> {
    return this.http.get<getNewsResp>('api/New_News/GetNew_NewsData', {
      params: {
        typeGroupId: typeGroupId,
        lang: this.shareService.getLang(),
      },
    });
  }

    getLineForView(menuitemId: string): Observable<getNewsResp> {
    return this.http.get<getNewsResp>('api/New_News/GetNew_NewsDataForView', {
      params: {
        typeGroupId: menuitemId,
        lang: this.shareService.getLang(),
      },
    });
  }
}
