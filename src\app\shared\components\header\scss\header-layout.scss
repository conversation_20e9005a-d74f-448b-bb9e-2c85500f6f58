// Scss Document
.header-layout{
	position: fixed;
	top: 0;
	width: 100%;
	z-index: 500;
}

.header{
	&-top{
		margin: 0;
		padding: 10px;
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		background-color: #fff;
		min-height: 70px;
		box-sizing: border-box;
		&-logo{
			padding-right: 10px;
			h1{
				margin: 0;
				padding: 0;
			}
			img{
				width: 100%;
			}
		}
		&-list{
			margin: 0;
			padding: 0;
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			align-items: center;
			&-infor{
				display: flex;
				flex-direction: row;
				justify-content: space-between;
				align-items: center;
				gap: 10px;
			}
			&-btn{
				display: flex;
				flex-direction: row;
				justify-content: space-between;
				align-items: center;
				text-decoration: none;
				color: #000;
				font-weight: bold;
				font-size: 1.25em;
			}
		}
	}
	//漢堡選單
	&-hamburger {
		&-btn {
			display: none;
			cursor: pointer;
			.header-hamburger-btn-closed-menu,
			.header-hamburger-btn-opened-menu {
				transition: opacity 0.3s ease;
			}
			// 預設狀態
			.header-hamburger-btn-closed-menu {
				display: block;
			}
			.header-hamburger-btn-opened-menu {
				display: none;
			}

			// 加上 .open 時，切換圖示
			&.open {
				.header-hamburger-btn-closed-menu {
					display: none;
				}
				.header-hamburger-btn-opened-menu {
					display: block;
				}
			}
		}
	}
}



//桌機選單
.nav-list-layout{
	display: flex;
	flex-direction: column;
    align-items: stretch;
    justify-content: flex-end;
    width: 100%;
	.nav-list{
		margin: 0;
		padding: 0;
		overflow-x: auto;
		background-color: #177691;
		&-cont{
			margin: 0;
			padding: 0;
			display: flex;
			flex-direction: row;
			justify-content: flex-end;
			list-style: none;
			width: max-content;
			min-width: 100%;
		}
		&-item{
			margin: 0;
			padding: 0;
			border-bottom: 6px solid transparent;
			transition: all .3s linear;
			&-btn{
				line-height: 1;
				padding: 16px 20px 10px 20px;
				display: block;
				color: #fff;
				text-decoration: none;
				font-size: 1.5625em;
				border: 0;
				background: transparent;
				cursor: pointer;
			}
			&.active,&:hover{
				border-bottom: 6px solid #fff;
				transition: all .3s ease;
			}
		}
	}
}
//第二層
.nav-second-cont {
	padding: 1em;
	background: #fff;
	margin: 20px;
	display: none;
	border-radius: 20px;
	box-shadow: 2px 2px 15px rgba(0, 0, 0, 0.1); 
	.nav-second-cont{
		&-list{
			display: grid;
			grid-template-columns: repeat(3, minmax(0, 1fr));
			gap: 1.5rem;
			&-item{
				.nav-third-cont{
					margin: 8px 0 0;
					.nav-second-cont-list-sublink{
						border-bottom: 0;
					}
				}
			}
			&-sublink{
				font-size: 1em;
				line-height: 1.625;
				letter-spacing: 0.125rem;
				font-weight: 700;
				padding: 8px 0;
				border-bottom: 2px solid #000;
				a{
					display: flex;
					align-items: center;
					justify-content: space-between;
					text-decoration: none;
					color: #000;
				}	
			}
		}
		
	}
}
.nav-second-cont.active {
	display: block;
}
//手機選單
.hamburger-content{
	display: none;
	position: fixed;
	z-index: 100;
	transition: all 0.3s ease;
	top: 70px;
	//padding: 70px 0 0;
	background-color: #fff;
	width: 100%;
	height: calc(100vh - 70px);
	z-index: 1000;
	overflow-y: auto;
	&.active {
		display: block;
	}
	&-nav{
		padding: 0px;
		box-sizing: border-box;
		&-group{
			display: flex;
			flex-direction: column;
			align-items: stretch;
			justify-content: flex-start;
			width: 100%;
		}
		&-link{
			&-button{
				padding: 11px 20px;
				display: flex;
				flex-direction: row;
				align-items: center;
				justify-content: space-between;
				box-sizing: border-box;
				width: 100%;
				text-align: left;
				border: 0;
				background-color: transparent;
				font-size: 1em;
				line-height: 1.625;
				letter-spacing: 0.125rem;
				font-weight: 700;
				text-decoration: none;
				color: #000;
				&.second-cont{
					padding: 11px 20px 11px 40px;
					background-color: #f3f3f5;
				}
				&.third-cont{
					padding: 11px 20px 11px 60px;
					background: #e1dfe5;
				}
			}
			&-icon{
				display: flex;
				flex-direction: row;
				align-items: center;
				justify-content: space-between;
				.on-open{
					display: block;
					height: 24px;
				}
				.on-close{
					display: block;
					height: 24px;
				}
			}
		}
	}
	.open{
		display: block;
	}
	.close{
		display: none;
	}
}

//無障礙
.hamburger-content-nav-link-button[aria-expanded="true"]{
	.on-open {
	  display: block;
	}
		.on-close {
	  display: none;
	}
} 


//文字大中小
.fontChange{
	margin: 0;
	padding: 0;
	display: flex;
	flex-direction: row;
	align-items: center;
	p{
		margin: 0;
		padding: 0;
		display: none;
	}
	ul{
		margin: 0;
		padding: 0;
		display: flex;
		li{
			margin: 0;
			padding: 0 2px;
			display: flex;
			overflow: hidden;
			line-height: 18px;
			width: 30px;
			text-align: center;
			flex-direction: row;
			align-items: center;
			a{
				padding: 5px 6px;
				display: block;
				overflow: hidden;
				border: 1px solid #177691;
				border-radius: 20px;
				background-color: #f0f0f0;
				text-decoration: none;
				color: #177691;
				width: 100%;
				box-sizing: border-box;
				&.font_s{
					font-size: 0.75em;
				}
				&.font_m{
					font-size: 1.25em;
				}
				&.font_l{
					font-size: 1.5em;
				}
				&:hover{
					background-color: #177691;
					border: 1px solid#177691;
					color: #fff;
				}		
			}
				
			&.active{
				a{
					color: #fff;
					background-color:#177691;
					border: 1px solid #177691;	
				}
					
			}
				
		}	
	}	
}


@media (max-width: 768px) {
	.header{
		//漢堡選單
		&-hamburger {
			&-btn {
				display: block;
			}
		}
	}

    .nav-list-layout, .header-top-list-infor {
        display: none;
    }
}
