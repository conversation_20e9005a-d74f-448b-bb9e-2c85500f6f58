import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ShareService } from './share.service';
import { getSurveyResp, sendSurveyReq } from '../interface/survey.interface';

@Injectable({
  providedIn: 'root',
})
export class SurveyService {
  constructor(
    private httpClient: HttpClient,
    private shareService: ShareService
  ) {}

  getSurvey(menuitemId: string): Observable<getSurveyResp> {
    return this.httpClient.get<getSurveyResp>('api/Survey/GetSurvey', {
      params: {
        menuitemId: menuitemId,
        lang: this.shareService.getLang(),
      },
    });
  }

  sendSurvey(req: sendSurveyReq) {
    return this.httpClient.post<any>('api/Survey/CreateSurveyResult', req);
  }
}
