import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { MenuItemService } from '../../core/services/menu-item.service';
import { MenuItem } from '../../shared/models/menu.model';
import { dataItem } from '../../core/interface/type.interface';
import { MenuType } from '../../enum/menyType.enum';
import { SpinnerService } from '../../core/utils/spinner.service';

@Component({
  selector: 'app-tab',
  standalone: false,

  templateUrl: './tab.component.html',
  styleUrl: './tab.component.scss',
})
export class TabComponent implements OnInit {
  menuItemId: string = '';
  menuType = MenuType;
  title: string = '';
  tabArr: MenuItem[] = [];
  tab: number = 0;
  selectedTabType: string = '';
  selectedTabIndex: number = 0;
  selectedTabId: string = '';
  contentData: dataItem[] = [];
  constructor(
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private menuItemService: MenuItemService,
    private spinnerService: SpinnerService
  ) {
    this.activatedRoute.queryParamMap.subscribe((params) => {
      this.menuItemId = params.get('menuItemId')!;
      this.tab = params.get('tab') ? Number(params.get('tab') as string) : 0;
    });
  }
  ngOnInit() {
    this.getTabList();
  }

  getTabList() {
    this.spinnerService.show();
    this.menuItemService.getTabList(this.menuItemId).subscribe({
      next: (resp: MenuItem) => {
        this.spinnerService.hide();
        console.log(resp);
        this.title = resp.name!;
        this.tabArr = resp.inverseParent;
        this.selectedTabType = this.tabArr[this.tab].type!;
        this.selectTab(
          this.tab,
          this.tabArr[this.tab]['id']!,
          this.tabArr[this.tab].type!
        );
      },
      error: () => {
        this.spinnerService.hide();
      },
    });
  }

  // getContentData() {
  //   switch (this.selectedTabType) {
  //     case 'Content':
  //       this.getContent();
  //       break;
  //     case 'HTML':
  //       this.getHtml();
  //       break;
  //     case 'HtmlZip':
  //       this.getHtmlZip();
  //       break;
  //     case 'ShareResource':
  //       this.getShareResource();
  //       break;
  //     case 'Video':
  //       this.getVideo();
  //       break;
  //     case 'FAQ':
  //       this.getFaq();
  //       break;
  //     case 'Survey':
  //       this.getSurvey();
  //       break;
  //   }
  // }

  selectTab(index: number, tabId: string, tabType: string, event?: Event) {
    if (event) {
      event.preventDefault();
    }
    this.tab = index;
    this.selectedTabType = tabType;
    this.selectedTabIndex = index;
    this.selectedTabId = tabId;
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: { menuItemId: this.menuItemId, tab: index }, // 這裡使用 index 更新 tab 參數
      // queryParamsHandling: 'merge', // 保留其它查詢參數
    });
  }
}
