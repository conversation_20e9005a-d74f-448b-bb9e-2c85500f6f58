import {
  Component,
  EventEmitter,
  Input,
  Output,
  SimpleChanges,
} from '@angular/core';

@Component({
  selector: 'app-paginator',
  templateUrl: './paginator.component.html',
  styleUrl: './paginator.component.scss',
  standalone: false,
})
export class PaginatorComponent {
  @Input() nowPage: number = 1;
  @Input() pageSize: number = 10;
  @Input() pageSizeOptions: number[] = [5, 10, 20];
  @Input() totalRecords: number = 20;
  @Input() pageIndex: number = 1;
  @Input() pageShowCount: number = 5;
  @Input() currentPageReportTemplate!: string;

  @Output() clickPageEvent = new EventEmitter<number>();
  @Output() pageSizeChangeEvent = new EventEmitter<number>();

  pagesData: any[] = [1, 2, 3, 4, 5, 6];
  showPagesData: any[] = [1, 2, 3, 4, 5, 6];
  pageReport = '第1到10 筆，共100筆';

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['totalRecords'] || changes['pageShowCount']) {
      this.getPageDataByTotal(changes['totalRecords'].currentValue);
    }

    if (changes['nowPage']) {
      this.getShowPages(changes['nowPage'].currentValue, this.pageShowCount);
    }
  }

  // page Size start

  changePageSize(e: any) {
    this.nowPage = 1;
    this.pageIndex = 1;
    this.getPageDataByTotal(this.totalRecords);
    this.pageSizeChangeEvent.emit(Number(e));
  }

  // page Size end

  // page report start

  // 把currentPageReportTemplate整理成藥顯示的字串  first:第
  organizePageReport(str: string, firstIndex: number) {
    const last =
      firstIndex * this.pageSize >= this.totalRecords
        ? this.totalRecords
        : firstIndex * this.pageSize;

    const replacements: any = {
      first: (firstIndex - 1) * this.pageSize + 1,
      last: last,
      totalRecords: this.totalRecords || '0',
    };
    const regex = /\{(.*?)\}/g;
    const replacedString = str.replace(regex, (match, p1) => {
      return replacements[p1] || match;
    });

    this.pageReport = replacedString;
  }

  // page report end

  // page btn start

  getPageDataByTotal(total: number) {
    const data = Array.from(
      { length: Math.ceil(total / this.pageSize) },
      (_, i) => i + 1
    );
    this.pagesData = data;
    this.showPagesData = data;
    this.getShowPages(this.pageIndex, this.pageShowCount);
  }

  getShowPages(pageIndex: number, showLastIndex: number) {
    let firstIndex = pageIndex;
    let lastIndex = showLastIndex - 1;
    let pageTotal = Math.ceil(this.totalRecords / this.pageSize);

    // 頁數切換時更換顯示筆數的詳細資料
    this.organizePageReport(this.currentPageReportTemplate, firstIndex);

    if (
      !(this.nowPage < this.pageIndex) &&
      this.nowPage <= this.pageIndex + lastIndex &&
      this.showPagesData.length === lastIndex + 1
    ) {
      return;
    }

    // 如果變更頁數後要顯示的頁數超過總頁數的話
    if (pageIndex + lastIndex >= pageTotal) {
      firstIndex = pageTotal - lastIndex;
    }

    this.pageIndex = pageIndex;
    const data = this.pagesData.filter(
      (page) => firstIndex <= page && pageIndex + lastIndex >= page
    );
    this.showPagesData = data;
  }

  clickPage(index: number) {
    if (this.nowPage === index) {
      return;
    }
    this.nowPage = index;
    this.getShowPages(index, this.pageShowCount);
    this.clickPageEvent.emit(index);
  }

  prevPage() {
    if (this.nowPage <= 1) {
      return;
    }
    const newPage = this.nowPage - 1;
    this.clickPage(newPage);
  }

  nextPage() {
    if (this.nowPage + 1 > Math.ceil(this.totalRecords / this.pageSize)) {
      return;
    }
    const newPage = this.nowPage + 1;
    this.clickPage(newPage);
  }

  firstPage() {
    const newPage = 1;
    this.clickPage(newPage);
  }
  lastPage() {
    const newPage = Math.ceil(this.totalRecords / this.pageSize);
    this.clickPage(newPage);
  }

  onInput(event: Event) {
    const totalPages = Math.ceil(this.totalRecords / this.pageSize);
    const input = event.target as HTMLInputElement;
    input.value = input.value.replace(/[^0-9]/g, '');
    const value = Number(input.value);
    if (value > totalPages) {
      input.value = totalPages.toString(); // 若超過總頁數則設為最大頁
    }
  }

  // page btn end
  changePage(event: Event) {
    const input = event.target as HTMLInputElement;
    const page = Number(input.value);
    this.clickPage(page);
  }
}
