import { defaultItem } from './share.interface';

export interface getLineListReq {
  menuitemId: string;
  currentPage: number;
  pageSize: number;
  lang: string;
}

export interface getLineListResp extends defaultItem {
  data: {
    menuitemId: string;
    title: string;
    totalCount: number;
    totalPage: number;
    currentPage: number;
    data: lineItem[];
  };
}

export interface lineItem {
  typeGroupId: string;
  new_NewsId: string;
  title: string;
  startTime: string;
  lang: string;
}
