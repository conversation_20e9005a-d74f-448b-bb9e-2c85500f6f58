import { defaultItem } from './share.interface';
import { dataItem } from './type.interface';

export interface getNewsListWithHomeResp extends defaultItem {
  data: {
    currentPage: number;
    menuitemId: string;
    title: string;
    totalCount: number;
    totalPage: number;
    data: newsItem[];
  };
}

export interface newsItem {
  content: string;
  coverDescription: string;
  coverUrl: string;
  lang: string;
  new_NewsId: string;
  startTime: string;
  title: string;
  type: string;
  typeGroupId: string;
  description: string;
}

export interface getNewsListReq {
  menuitemId: string;
  currentPage: number;
  pageSize: number;
  search: string;
  newsType: string;
  userGroupId: string;
  lang: string;
}

export interface getNewsListResp extends defaultItem {
  data: {
    title: string;
    totalCount: number;
    totalPage: number;
    currentPage: number;
    data: newsItem[];
  };
}

export interface newsListItem {
  coverDescription: string;
  coverUrl: string;
  description: string;
  lang: string;
  new_NewsId: string;
  startTime: string;
  title: string;
  type: string;
  typeGroupId: string;
}

export interface getNewsResp extends defaultItem {
  data: {
    menuitemId: string;
    new_NewsId: string;
    title: string;
    type: string;
    coverDataId: string;
    startTime: string;
    new_NewsTagDatas: dataItem[];
    menuitemParent: string;
    lang: string;
    typeGroupId: string;
  };
}
