import {
  Component,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { dataItem } from '../../core/interface/type.interface';
import { TagType } from '../../enum/tagType.enum';

@Component({
  selector: 'app-template',
  standalone: false,

  templateUrl: './template.component.html',
  styleUrl: './template.component.scss',
})
export class TemplateComponent implements OnInit, OnChanges {
  @Input() contentData: dataItem[] = [];
  tagType = TagType;
  constructor() {}

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges): void {
    if (
      changes['contentData'] &&
      changes['contentData'].currentValue.length > 0
    ) {
      console.log(this.contentData);
    }
  }
}
