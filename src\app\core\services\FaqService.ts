import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { getFaqListReq, getFaqListResp } from '../interface/faq.interface';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class FaqService {
  constructor(private httpClient: HttpClient) {}

  getFaqList(req: getFaqListReq): Observable<getFaqListResp> {
    return this.httpClient.get<getFaqListResp>('api/Faq/GetFAQList', {
      params: {
        ...req,
      },
    });
  }

  getFaqListForView(id: string): Observable<getFaqListResp> {
    return this.httpClient.get<getFaqListResp>('api/Faq/GetFAQDataForView', {
      params: {
        typeGroupId: id,
      },
    });
  }
}
