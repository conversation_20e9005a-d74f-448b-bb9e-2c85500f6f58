import { sportDataItem } from './sport.model';
import { faqDataModel } from "./faq.model";
import { guidlineDataModel } from "./guideline.model";
import { sportDataModel, sportItem, sportPlaceDataModel, sportPlaceItem } from "./sports.model";
import { videoItem } from './video.model';

export interface resModel {
  message: string;
  data: listDataModel | faqDataModel | guidlineDataModel | sportDataModel | homeDataModel;
}

export interface listDataModel {
  title: string;
  totalCount: number;
  totalPage: number;
  currentPage: number;
  data?: listDataItem[] | imgDataItem[] | sportItem[] | sportDataItem[] | sportPlaceItem[] | videoItem[];
}

export interface listDataItem {
  new_NewsId: string;
  title: string;
  startTime: string;
  type: string;
  coverUrl: string;
}

export interface imgDataItem extends listDataItem {
  coverUrl: string;
}


export interface newsListReq {
  menuitemId: string;
  currentPage?: number;
  pageSize?: number;
  search?: string;
  newsType?: string;
  sportType?: string;
}

export interface listReq {
  menuitemId: string;
  currentPage: number;
  pageSize: number;
}

export interface homeDataModel {
  menuitemId: string;
  title: string;
  totalCount: number;
  totalPage: number;
  currentPage: number;
  data: videoItem[] | listDataItem[];
}
