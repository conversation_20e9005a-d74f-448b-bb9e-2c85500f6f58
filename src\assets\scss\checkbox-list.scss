// Scss Document
.checkbox-list{
	padding: 0 15px 0 0;
	//display: inline-block;
	position: relative;
	display: inline-flex;
    align-items: center;
	gap: 0.2rem;
	label{
		display: inline-block;
		font-size: 1.125em;
		width: max-content;
		//color: #232127;
	}
		
	input[type="checkbox"]{
		margin: 0;
		display: inline-block;
		position: relative;
		top: 0;
		-webkit-appearance: none;
		background: #fff;
		border: #949494 solid 1px;
		border-radius: 5px;
		min-height: 25px;
		min-width: 25px;
		&:checked{
			background: #177691;
			border: #177691 solid 1px;
			&::after{
				content: '';
				top: 4px;
				left: 5px;
				position: absolute;
				background: transparent;
				border: #fff solid 3px;
				border-top: none;
				border-right: none;
				height: 8px;
				width: 10px;
				-moz-transform: rotate(-45deg);
				-ms-transform: rotate(-45deg);
				-webkit-transform: rotate(-45deg);
				transform: rotate(-45deg);
			}	
		}
		&:disabled{
			background-color: #E7E8E9;
			border: #E7E8E9 solid 1px;
		}
			
	}
		
}	
