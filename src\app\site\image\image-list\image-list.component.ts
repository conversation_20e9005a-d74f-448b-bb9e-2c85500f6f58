import { Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ImageService } from '../../../core/services/image.service';
import { ShareService } from '../../../core/services/share.service';
import {
  getImageListReq,
  getImageListResp,
  imageItem,
} from '../../../core/interface/image.interface';
import { SpinnerService } from '../../../core/utils/spinner.service';

@Component({
  selector: 'app-image-list',
  standalone: false,

  templateUrl: './image-list.component.html',
  styleUrl: './image-list.component.scss',
})
export class ImageListComponent {
  menuItemId: string = '';
  title: string = '';
  imageList: imageItem[] = [];
  nowPage: number = 1;
  pageSize: number = 10;
  pageShowCount: number = 5; //分頁器秀幾個
  totalPage: number = 0;
  totalCount: number = 0;
  constructor(
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private shareService: ShareService,
    private imageService: ImageService,
    private spinnerService: SpinnerService
  ) {
    this.activatedRoute.queryParamMap.subscribe((params) => {
      this.menuItemId = params.get('menuItemId')!;
    });
  }
  ngOnInit() {
    this.getImageList();
  }

  getImageList() {
    let req: getImageListReq = {
      menuitemId: this.menuItemId,
      currentPage: this.nowPage,
      pageSize: this.pageSize,
      lang: this.shareService.getLang(),
    };
    this.spinnerService.show();
    this.imageService.getImageList(req).subscribe({
      next: (resp: getImageListResp) => {
        this.spinnerService.hide();
        this.title = resp.data.title;
        this.totalCount = resp.data.totalCount;
        this.totalPage = resp.data.totalPage;
        this.imageList = resp.data.data;
      },
      error: () => {
        this.spinnerService.hide();
      },
    });
  }

  /**
   * 第幾頁
   * @param item number
   */
  getPageFromPaginator(nowPage: number) {
    this.nowPage = nowPage;
    this.getImageList();
  }
  /**
   * 每頁資料數量
   * @param item number
   */
  getPageSizeFromPaginator(pageSize: number) {
    this.pageSize = pageSize;
    this.nowPage = 1;
    this.getImageList();
  }

  goImage(typeGroupId: string) {
    this.router.navigate(['/ImgDetail'], {
      queryParams: { typeGroupId: typeGroupId },
    });
  }
}
