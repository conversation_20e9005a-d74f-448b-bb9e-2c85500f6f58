import { Component, OnInit } from '@angular/core';
import { ShareService } from '../../../core/services/share.service';
import {
  getTypeListResp,
  typeItem,
} from '../../../core/interface/share.interface';
import { FormBuilder, FormGroup } from '@angular/forms';
import { NewsService } from '../../../core/services/news.service';
import {
  getNewsListReq,
  getNewsListResp,
  newsListItem,
} from '../../../core/interface/news.interface';
import { ActivatedRoute, Router } from '@angular/router';
import { SpinnerService } from '../../../core/utils/spinner.service';

@Component({
  selector: 'app-news-list',
  standalone: false,

  templateUrl: './news-list.component.html',
  styleUrl: './news-list.component.scss',
})
export class NewsListComponent implements OnInit {
  form: FormGroup;
  menuItemId: string = '';
  title: string = '';
  typeList: typeItem[] = [];

  newsList: newsListItem[] = [];
  nowPage: number = 1;
  pageSize: number = 10;
  pageShowCount: number = 5; //分頁器秀幾個
  totalPage: number = 0;
  totalCount: number = 0;

  constructor(
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private shareService: ShareService,
    private fb: FormBuilder,
    private newsService: NewsService,
    private spinnerService: SpinnerService
  ) {
    this.activatedRoute.queryParamMap.subscribe((params) => {
      this.menuItemId = params.get('menuItemId')!;
    });
    this.form = this.fb.group({
      newsType: [''],
      keyWord: [''],
    });
  }

  ngOnInit() {
    this.getTypeList();
    this.getNewsList();
  }

  getTypeList() {
    this.shareService.getTypeList('類型選項').subscribe({
      next: (resp: getTypeListResp) => {
        this.typeList = resp.data;
      },
      error: () => {},
    });
  }

  getNewsList() {
    let req: getNewsListReq = {
      menuitemId: this.menuItemId,
      currentPage: this.nowPage,
      pageSize: this.pageSize,
      search: this.form.value.keyWord,
      newsType: this.form.value.newsType,
      userGroupId: '',
      lang: this.shareService.getLang(),
    };
    this.spinnerService.show();
    this.newsService.getNewsList(req).subscribe({
      next: (resp: getNewsListResp) => {
        this.spinnerService.hide();
        this.title = resp.data.title;
        this.totalCount = resp.data.totalCount;
        this.totalPage = resp.data.totalPage;
        this.newsList = resp.data.data;
      },
      error: () => {
        this.spinnerService.hide();
      },
    });
  }

  search() {
    this.nowPage = 1;
    this.getNewsList();
  }

  /**
   * 第幾頁
   * @param item number
   */
  getPageFromPaginator(nowPage: number) {
    this.nowPage = nowPage;
    this.getNewsList();
  }
  /**
   * 每頁資料數量
   * @param item number
   */
  getPageSizeFromPaginator(pageSize: number) {
    this.pageSize = pageSize;
    this.nowPage = 1;
    this.getNewsList();
  }

  goNews(typeGroupId: string) {
    this.router.navigate(['/NewsDetail'], {
      queryParams: { typeGroupId: typeGroupId },
    });
  }
}
