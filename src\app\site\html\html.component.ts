import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { HtmlService } from '../../core/services/html.service';
import { getHtemlDataResp } from '../../core/interface/html.interface';

@Component({
  selector: 'app-html',
  standalone: false,

  templateUrl: './html.component.html',
  styleUrl: './html.component.scss',
})
export class HtmlComponent {
  menuItemId: string = '';
  title: string = '';
  content: string = '';
  constructor(
    private activatedRoute: ActivatedRoute,
    private htmlService: HtmlService
  ) {
    this.activatedRoute.queryParamMap.subscribe((params) => {
      this.menuItemId = params.get('menuItemId')!;
    });
  }

  ngOnInit(): void {
    this.getContent();
  }

  getContent() {
    this.htmlService.getHtemlData(this.menuItemId).subscribe({
      next: (resp: getHtemlDataResp) => {
        this.title = resp.data.title;
        this.content = resp.data.content;
      },
      error: () => {},
    });
  }
}
