import { Component, Inject, Renderer2 } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { LangChangeEvent, TranslateService } from '@ngx-translate/core';
import { DOCUMENT } from '@angular/common';
import { Subscription } from 'rxjs';
import { LANGUAGE } from '../../enum/language.enum';
import { LoadingService } from '../../shared/utilities/loading.service';

@Component({
  selector: 'app-index',
  standalone: false,

  templateUrl: './index.component.html',
  styleUrl: './index.component.scss',
})
export class IndexComponent {
  private langChangeSubscription!: Subscription;
  language: string = '';
  quickLinksOpen: boolean = false;

  constructor(
    @Inject(DOCUMENT) private document: Document,
    private router: Router,
    private route: ActivatedRoute,
    private renderer: Renderer2,
    private translate: TranslateService
  ) {
    this.translate.setDefaultLang('zh-tw');
    this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        this.language = sessionStorage.getItem('language')
          ? (sessionStorage.getItem('language') as string)
          : LANGUAGE.TW;
      }
    });
  }
  ngOnInit(): void {
    this.langChangeSubscription = this.translate.onLangChange.subscribe(
      (event: LangChangeEvent) => {
        let lang = '';
        switch (event.lang) {
          case LANGUAGE.TW:
            lang = 'zh-Hant-tw';
            break;
          case LANGUAGE.EN:
            lang = 'en';
            break;
          case LANGUAGE.JP:
            lang = 'ja';
            break;
          case LANGUAGE.KR:
            lang = 'ko';
            break;
        }
        if (event.lang)
          // 當語言變更時，更新 <html> 的 lang 屬性
          this.renderer.setAttribute(
            this.document.documentElement,
            'lang',
            lang
          );
      }
    );
  }

  ngOnDestroy() {
    // 元件銷毀時，取消訂閱以避免記憶體洩漏
    if (this.langChangeSubscription) {
      this.langChangeSubscription.unsubscribe();
    }
  }

  scrollToTop() {
    // 這些頁面禁用滾動
    if (
      this.router.url.includes('/sports') ||
      (this.router.url.includes('/news') &&
        !this.router.url.includes('/news-detail'))
    ) {
      return;
    }

    window.scroll({
      top: 0,
      left: 0,
      behavior: 'smooth',
    });
  }

  scrollToTop2() {
    window.scroll({
      top: 0,
      left: 0,
      behavior: 'smooth',
    });
    document.getElementById('header')?.focus();
  }
}
