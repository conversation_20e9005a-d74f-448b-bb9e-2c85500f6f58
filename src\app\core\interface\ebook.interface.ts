import { defaultItem } from './share.interface';

export interface getEbookListReq {
  menuitemId: string;
  currentPage: number;
  pageSize: number;
  lang: string;
}

export interface getEbookListResp extends defaultItem {
  data: {
    menuitemId: string;
    title: string;
    totalCount: number;
    totalPage: number;
    currentPage: number;
    data: ebookItem[];
  };
}
export interface ebookItem {
  title: string;
  littleTile: string;
  littleTitleZh: string;
  period: string;
  bookId: string;
  coverUrl: string;
}

export interface getEbookResp extends defaultItem {
  data: {
    bookId: string;
    title: string;
    littleTile: string;
    littleTitleZh: string;
    period: string;
    coverUrl: string;
    ebookPreviewUrl: string;
    introduction: string;
    outline: string;
    date: string;
    author: string;
  };
}
