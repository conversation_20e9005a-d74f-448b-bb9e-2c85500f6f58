import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { getHtemlDataResp } from '../../core/interface/html.interface';
import { HtmlService } from '../../core/services/html.service';
import { environment } from '../../../environments/environment';
import { ShareService } from '../../core/services/share.service';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';

@Component({
  selector: 'app-html-zip',
  standalone: false,

  templateUrl: './html-zip.component.html',
  styleUrl: './html-zip.component.scss',
})
export class HtmlZipComponent {
  menuItemId: string = '';
  title: string = '';
  content: string = '';
  url: SafeResourceUrl | undefined;
  constructor(
    private activatedRoute: ActivatedRoute,
    private shareService: ShareService,
    private sanitizer: DomSanitizer
  ) {
    this.activatedRoute.queryParamMap.subscribe((params) => {
      this.menuItemId = params.get('menuItemId')!;
    });
  }

  ngOnInit(): void {
    let rawUrl = `${
      environment.apiUrl
    }/static/htmlIndex/${this.shareService.getLang()}/${
      this.menuItemId
    }/index.html`;

    this.url = this.sanitizer.bypassSecurityTrustResourceUrl(rawUrl);
  }
}
