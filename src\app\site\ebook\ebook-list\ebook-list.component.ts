import { Component } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { EbookService } from '../../../core/services/ebook.service';
import {
  ebookItem,
  getEbookListReq,
  getEbookListResp,
} from '../../../core/interface/ebook.interface';
import { ShareService } from '../../../core/services/share.service';
import { SpinnerService } from '../../../core/utils/spinner.service';

@Component({
  selector: 'app-ebook-list',
  standalone: false,
  templateUrl: './ebook-list.component.html',
  styleUrl: './ebook-list.component.scss',
})
export class EbookListComponent {
  menuItemId: string = '';
  title: string = '';
  ebookList: ebookItem[] = [];
  nowPage: number = 1;
  pageSize: number = 10;
  pageShowCount: number = 5; //分頁器秀幾個
  totalCount: number = 0;

  constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private ebookService: EbookService,
    private shareService: ShareService,
    private spinnerService: SpinnerService
  ) {
    this.activatedRoute.queryParamMap.subscribe((params) => {
      this.menuItemId = params.get('menuItemId')!;
      this.getEbookList();
    });
  }

  getEbookList() {
    let req: getEbookListReq = {
      menuitemId: this.menuItemId,
      currentPage: this.nowPage,
      pageSize: this.pageSize,
      lang: this.shareService.getLang(),
    };
    this.spinnerService.show();
    this.ebookService.getEbookList(req).subscribe({
      next: (resp: getEbookListResp) => {
        this.spinnerService.hide();
        this.title = resp.data.title;
        this.totalCount = resp.data.totalCount;
        this.ebookList = resp.data.data;
      },
      error: () => {
        this.spinnerService.hide();
      },
    });
  }

  /**
   * 第幾頁
   * @param item number
   */
  getPageFromPaginator(nowPage: number) {
    this.nowPage = nowPage;
    this.getEbookList();
  }
  /**
   * 每頁資料數量
   * @param item number
   */
  getPageSizeFromPaginator(pageSize: number) {
    this.pageSize = pageSize;
    this.nowPage = 1;
    this.getEbookList();
  }
}
