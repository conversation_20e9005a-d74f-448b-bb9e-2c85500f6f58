// Scss Document
//按鈕//
.btn {
	font-size: initial !important;
}

.btn-list {
	margin: 0px 5px;
	padding: 12px 20px;
	-moz-user-select: none;
	user-select: none;
	background-image: none;
	border: 1px solid transparent;
	border-radius: 5px;
	cursor: pointer;
	display: inline-block;
	font-weight: 400;
	line-height: 1.2;
	text-align: center;
	font-size: 1.125em;
}

.btn-primary-color {
	background: #177691;
	color: #fff;
	&:hover,
	&:focus {
		background: #177691;
		opacity: 0.5;
	}
	&:active,
	&.active {
		background-color: #146880;
		border-color: #146880;
	}
}

.btn-new-color {
	background: #dda300;
	color: #fff;
	&:hover,
	&:focus {
		background: #dda300;
		opacity: 0.5;
	}
	&:active,
	&.active {
		background-color: #bd8b00;
		border-color: #bd8b00;
	}
}
.btn-modify-color {
	background: #b82e0f;
	color: #fff;
	&:hover,
	&:focus {
		background: #b82e0f;
		opacity: 0.5;
	}
	&:active,
	&.active {
		background-color: #c53029;
		border-color: #c53029;
	}
}

.btn-normal-color {
	background: #2f8339;
	color: #fff;
	&:hover,
	&:focus {
		background: #2f8339;
		opacity: 0.5;
	}
	&:active,
	&.active {
		background-color: #296c4d;
		border-color: #296c4d;
	}
}

.btn-default-color {
	background-color: #fafafa;
	border-color: #177691;
	color: #177691;
	text-shadow: 0 1px 0 #fff;
	&:hover,
	&:focus {
		background-color: #fafafa;
		opacity: 0.5;
	}
	&:active,
	&.active {
		background-color: #e0e0e0;
		border-color: #177691;
	}
}

.btn-link-color {
	border-radius: 0;
	color: #177691;
	font-weight: 400;
	background-color: transparent;
	box-shadow: none;
	&:active,
	&.active,
	&[disabled] {
		background-color: transparent;
		box-shadow: none;
	}
	&:hover,
	&:focus,
	&:active {
		border-color: transparent;
	}
	&:hover,
	&:focus {
		background-color: transparent;
		color: #23527c;
		text-decoration: underline;
	}
	&[disabled]:hover {
		color: #e7e8e9;
		text-decoration: none;
	}
}

.btn-primary-color,
.btn-new-color,
.btn-modify-color,
.btn-normal-color,
.btn-default-color {
	&:disabled,
	&[disabled] {
		border-color: #e7e8e9;
		background-color: #e7e8e9;
		color: #333;
		cursor: default;
	}
}
