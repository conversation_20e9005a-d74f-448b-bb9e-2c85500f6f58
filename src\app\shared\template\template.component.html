<!--內容放置區-->
<div class="pages-content">
    <div class="pages-content-lsit">
        @for(item of contentData; track item){
        @switch (item.tagName) {
        @case (tagType.ParagraphTitle) {
        <app-paragraph-title [paragraphTitleData]="item.tagData"></app-paragraph-title>
        }
        @case (tagType.Title) {
        <app-title [titleData]="item.tagData"></app-title>
        }
        @case (tagType.Content) {
        <app-content-template [contentData]="item.tagData"></app-content-template>
        }
        @case (tagType.Html) {
        <app-html-template [htmlData]="item.tagData"></app-html-template>
        }
        @case (tagType.Photo) {
        <app-photo [photoData]="item.tagData"></app-photo>
        }
        @case (tagType.PhotoList) {
        <app-photo-list [photoListData]="item.tagData"></app-photo-list>
        }
        @case (tagType.Video) {
        <app-video-template [videoData]="item.tagData"></app-video-template>
        }
        @case (tagType.Address) {
        <app-address [addressData]="item.tagData"></app-address>
        }
        @case (tagType.FileData) {
        <app-file-data [fileData]="item.tagData"></app-file-data>
        }
        @case (tagType.Button) {
        <app-button [buttonData]="item.tagData"></app-button>
        }
        @case (tagType.Card) {
        <app-card [cardData]="item.tagData"></app-card>
        }
        @case (tagType.Table) {
        <app-table [tableData]="item.tagData"></app-table>
        }
        }
        }
    </div>
</div>