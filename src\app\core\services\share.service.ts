import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { getTypeListResp } from '../interface/share.interface';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';
import { TranslateService } from '@ngx-translate/core';

@Injectable({
  providedIn: 'root',
})
export class ShareService {
  constructor(
    private httpClient: HttpClient,
    private sanitizer: DomSanitizer,
    private translate: TranslateService
  ) {}

  getLang(): string {
    return sessionStorage.getItem('lang') || 'zh';
  }

  getTypeList(type: string): Observable<getTypeListResp> {
    return this.httpClient.get<getTypeListResp>(
      'api/Manage/New_News/GetTypeList',
      {
        params: {
          type: type,
          lang: this.getLang(),
        },
      }
    );
  }

  getSafeImageUrl(blob: Blob): SafeUrl {
    if (blob) {
      return this.sanitizer.bypassSecurityTrustUrl(URL.createObjectURL(blob));
    }
    return '';
  }

  initLanguage() {
    this.translate.setDefaultLang('zh');
    this.translate.use('zh');
  }
}
