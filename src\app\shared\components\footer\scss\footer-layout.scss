// Scss Document

.footer-layout{
	margin: 0;
	padding: 0;
	background-color: #202D3F;
	.footer-container{
		padding: 30px;
		color: #fff;
		font-size: 1em;
		line-height: 1.6;
		letter-spacing: 0.125rem;
		&-list{
			display: flex;
			flex-wrap: wrap;
			justify-content:space-between;
			&-item{
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: flex-start;
				gap: 5px;
				&-title{
					margin: 0;
					padding: 0;
					font-weight: bold;
				}
				&-form{
					display: flex;
					flex-direction: row;
					align-items: center;
					&-input{
						padding: 9px 2px 9px 16px;
						background: rgba(243, 243, 245, 0.2);
						border: 1px solid #fff;
						max-width: 260px;
						border-radius: 40px;
						font-weight: 700;
						width: 100%;
						-webkit-appearance: none;
					}
					&-btn{
						display: inline-block;
						padding: 9px 16px;
						border-radius: 22px;
						font-weight: 700;
						position: relative;
						white-space: nowrap;
						color: #000;
						background-color: #fff;
						transition: background-color 0.1s ease;
					}
				}
				&-hint{
					margin-top: 8px;
					display: flex;
					flex-direction: row;
					align-items: center;
					font-size: 0.875em;
					font-weight: 400;
					line-height: 1.4;
					span{
						padding-right: 5px;
					}
				}
				a{
					display: block;
					overflow: hidden;
					color: #fff;
					text-decoration: none;
					font-weight: normal;
				}
				p{
					margin: 0;
					padding: 0;
					display: flex;
					flex-direction: row;
					align-items: flex-start;
					font-weight: bold;
					span{
						display: inline-block;
						width: 72px;
					}
				}
				//社群
				.community-layout{
					display: flex;
					flex-direction: row;
					align-items: stretch;
					flex-wrap: wrap;
					gap: 10px;
					justify-content: flex-end;
					width: 100%;
					.community-button, .community-link-button{
						display: flex;
						align-items: center;
						background-color: #fff;
						color: #202D3F;
						border-radius: 80px;
						font-weight: bold;
					}
					.community-link-button{
						padding: 0 20px;
					}
				}
				.sitmap-layout{
					display: flex;
					flex-direction: row;
					align-items: stretch;		
					justify-content: flex-end;
					flex-wrap: wrap;
					gap: 10px;
					width: 100%;
					.sitmap-button{
						display: flex;
						flex-direction: row;
						align-items: center;
						justify-content: flex-start;
					}
				}
			}
		}
		&-hr{
				margin: 30px 0;
				border-top: 1px solid #fff;
			}
	}
}


.display_none{
	display: none !important;
}

.hidden{
	display: none;
}

@media (max-width: 1135px) {
	.footer-list-layout{
		.footer-container{
			&-list{
				flex-direction: column;
				align-items: center;
				gap: 10px;
				&:last-child{
					.footer-container-list-item{
						width: 100%;
					}
				}
				//社群
				.community-layout, .sitmap-layout{
					justify-content: flex-start
				}
			}
		}
	}
}

