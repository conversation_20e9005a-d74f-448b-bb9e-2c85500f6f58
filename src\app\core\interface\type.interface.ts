// 內文區
export interface ContentData {
  content: string;
}

// 檔案區
export interface FileData {
  fileName: string;
  fileId: string;
  linkUrl?: string;
  fileType?: string;
}

export interface FileDataList {
  filedatalist: FileData[];
}

// 標題
export interface TitleData {
  title: string;
}

// 段落標題
export interface ParagraphTitleData {
  title: string;
}

// 圖片(單檔)
export interface PhotoData {
  fileUrl: string;
  fileDescription?: string;
}

// 圖片(多檔)
export interface PhotoDatas {
  photoDatas: PhotoData[];
}

// 影片
export interface VideoData {
  type: string;
  fileUrl: string;
}

// 地址
export interface AddressData {
  address: string;
}

// 按鈕區
export interface ButtonData {
  title: string;
  url: string;
}

// HTML
export interface HtmlData {
  htmlString: string;
}

// 入口圖卡
export interface CardData {
  cardUrl: string;
  cardName: string;
  cardUrlName: string;
}

// 表格區
export interface TableData {
  fileName: string;
  fileId: string;
}

export interface TableDataList {
  contentData: [][];
  xData: string[];
  yData: string[];
}

export interface ContentItem {
  tagName: 'content';
  tagData: ContentData;
  sort: number;
  tagId: string;
}

export interface FileListItem {
  tagName: 'fileData';
  tagData: FileDataList;
  sort: number;
  tagId: string;
}

export interface TitleItem {
  tagName: 'title';
  tagData: TitleData;
  sort: number;
  tagId: string;
}

export interface ParagraphTitleItem {
  tagName: 'paragraphTitle';
  tagData: ParagraphTitleData;
  sort: number;
  tagId: string;
}

export interface PhotoItem {
  tagName: 'photo';
  tagData: PhotoData;
  sort: number;
  tagId: string;
}

export interface PhotoDatasItem {
  tagName: 'photoList';
  tagData: PhotoDatas;
  sort: number;
  tagId: string;
}

export interface VideoItem {
  tagName: 'video';
  tagData: VideoData;
  sort: number;
  tagId: string;
}

export interface AddressItem {
  tagName: 'address';
  tagData: AddressData;
  sort: number;
  tagId: string;
}

export interface ButtonItem {
  tagName: 'button';
  tagData: ButtonData;
  sort: number;
  tagId: string;
}

export interface HtmlItem {
  tagName: 'html';
  tagData: HtmlData;
  sort: number;
  tagId: string;
}

export interface CardItem {
  tagName: 'card';
  tagData: CardData;
  sort: number;
  tagId: string;
}

export interface TableListItem {
  tagName: 'table';
  tagData: TableDataList;
  sort: number;
  tagId: string;
}

// 最終的聯合型別
export type dataItem =
  | ContentItem
  | FileListItem
  | TitleItem
  | ParagraphTitleItem
  | PhotoItem
  | PhotoDatasItem
  | VideoItem
  | AddressItem
  | ButtonItem
  | HtmlItem
  | CardItem
  | TableListItem;
