import { Overlay, OverlayRef } from '@angular/cdk/overlay';
import { ComponentPortal } from '@angular/cdk/portal';
import { Injectable } from '@angular/core';
import { SpinnerComponent } from '../../spinner/spinner.component';

@Injectable({
  providedIn: 'root',
})
export class SpinnerService {
  private overlayRef?: OverlayRef;
  private isSpinnerVisible = false;

  constructor(private overlay: Overlay) {}

  public show(message = '') {
    if (!this.overlayRef) {
      this.overlayRef = this.overlay.create();
    }

    if (!this.overlayRef.hasAttached()) {
      const spinnerOverlayPortal = new ComponentPortal(SpinnerComponent);
      this.overlayRef.attach(spinnerOverlayPortal);
      this.isSpinnerVisible = true;
    }
  }

  public hide() {
    if (this.overlayRef?.hasAttached() && this.isSpinnerVisible) {
      this.overlayRef.detach();
      this.isSpinnerVisible = false;
    }
  }
}
